import { EventEmitter,errorMonitor } from 'node:events';


// MyEmitter {
//     _events: [Object: null prototype] {
//            event:[Function(anonymous)]  // 多个监听，这里就会添加
//     },
//     _eventsCount: 1,
//     _maxListeners: undefined,
//     [Symbol(kCapture)]: false
// }
class MyEmitter extends EventEmitter { }

const myEmitter = new MyEmitter();
let m = 0;

// once 只能掉一次
myEmitter.on('event', () => {
    console.log(++m);
});
myEmitter.on('event', () => {
    console.log('第二个event');
});
console.log('myEmitter', myEmitter)
myEmitter.emit('event');
myEmitter.emit('event');



// myEmitter.on(errorMonitor, (err) => {
//     MyMonitoringTool.log(err);
// });
// myEmitter.emit('error', new Error('whoops!'));
// Still throws and crashes Node.js

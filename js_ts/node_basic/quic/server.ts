import {createConnection} from 'node:net';
import fs from 'fs';

const key = fs.readFileSync('server-key.pem');
const cert = fs.readFileSync('server-cert.pem');

const server = createQuicSocket({ endpoint: { port: 12345 } });

server.on('session', (client) => {
  console.log('Client connected.');

  client.on('stream', (stream) => {
    stream.setEncoding('utf8');
    stream.on('data', (data) => {
      console.log(`Received from client: ${data}`);
      stream.write(`Server received: ${data}`);
    });
  });
});

server.listen({ key, cert, alpn: 'example' }, () => {
  console.log('Server is listening on port 12345');
});

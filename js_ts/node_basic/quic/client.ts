import { createQuicSocket } from 'node:quic';
import { readFileSync } from 'node:fs';

const client = createQuicSocket({ client: { key: readFileSync('./client-key.pem'), cert: readFileSync('./client-cert.pem') } });

const req = client.connect({
  address: 'localhost',
  port: 8000,
  alpn: 'hello'
});

req.on('secure', () => {
  const stream = req.openStream();
  stream.write('Hello from client!');
  
  stream.on('data', (chunk) => {
    console.log('Received from server:', chunk.toString());
    stream.end();
  });

  stream.on('end', () => {
    console.log('Stream ended');
    req.close();
  });
});

req.on('close', () => {
  console.log('Connection closed');
  client.close();
});
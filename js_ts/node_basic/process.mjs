import process from 'node:process';

process.on('beforeExit', (code) => {
    console.log('Process beforeExit event with code: ', code);
});

process.on('exit', (code) => {
    console.log('Process exit event with code: ', code);
});




const unhandledRejections = new Map();
// 均和promise有关
process.on('unhandledRejection', (reason, promise) => {
    unhandledRejections.set(promise, reason);
});
process.on('rejectionHandled', (promise) => {
    unhandledRejections.delete(promise);
});




// Begin reading from stdin so the process does not exit.
process.stdin.resume();

process.on('SIGINT', () => {
    console.log('Received SIGINT. Press Control-D to exit.');
});

// Using a single function to handle multiple signals
function handle(signal) {
    console.log(`Received ${signal}`);
}

process.on('SIGINT', handle);  // ctrl c触发
process.on('SIGTERM', handle); // ctrl c触发

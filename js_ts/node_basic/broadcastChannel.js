'use strict';
const {
    is<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>Chan<PERSON>,
    Worker
} = require('node:worker_threads');



const bc = new BroadcastChannel('hello');
if (isMainThread) {
    let c = 0;
    bc.onmessage = (event) => {
        console.log(event.data);
        if (++c === 10) bc.close();
    };
    for (let n = 0; n < 10; n++) {
        new Worker(__filename);
    }
} else {
    bc.postMessage('hello from every worker');
    bc.close();
}
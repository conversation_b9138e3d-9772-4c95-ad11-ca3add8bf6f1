const fs = require('fs');

console.log('test')

const canWrite = process.permission.has('fs.write');
const canWriteTest = process.permission.has('fs.write', '/Users/<USER>/');

console.log('canWrite:', canWrite)
console.log('canWriteTest:', canWriteTest)

const canRead = process.permission.has('fs.read');
const canReadTest = process.permission.has('fs.read', '/Users/<USER>/');

console.log('canRead:', canRead)
console.log('canReadTest:', canReadTest)

fs.readFile('./test.txt', 'utf-8', (err, data) => {
  if(err) return console.log('err:', err)
  console.log('data:', data)
})

fs.writeFile('./test.txt', 'test write', (err) => {
  if(err) return console.log('err:', err)
  console.log('write success')
})
// node --experimental-permission --allow-fs-read=/Users/<USER>/  --allow-fs-write=/Users/<USER>/ index.js


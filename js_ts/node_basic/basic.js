for (let index = 0; index <= 100; index++) {
    if (index == 30) {
        continue; //循环跳过这一步
        break;//结束循环

    } else {
        console.log('index:', index)
    }
}


// class Animal {
//     constructor (type, name, age, sex) {
//         this.name = name
//         this.age = age
//         this.sex = sex
//         this.type = type
//     }

//     shout() {
//         if (this.type == 'human') {
//             console.log("人叫")
//         } else if (this.type == 'dog') {
//             console.log("汪汪")
//         } else if (this.type == 'cat') {
//             console.log("喵喵")
//         }
//     }
// }

// let lijiaxu = new Animal("human", "李家绪", "男", 29)
// let lijiaxudegou = new Animal("dog", "旺狗", "男", 3)
// let lijiaxudemao = new Animal("cat", "喵猫", "女", 1)

// lijiaxu.shout()
// lijiaxudegou.shout()
// lijiaxudemao.shout()
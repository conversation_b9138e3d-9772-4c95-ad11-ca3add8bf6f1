// 二进制
// 0b111110111 === 503 // true
// 0o767 === 503 // true
// 0x12

// 科学
// let budget = 1_000_000_000_000;
// 小数 0.000_001
// 科学计数法 1e10_000
// 二进制 0b1010_0001_1000_0101
// 十六进制 0xA0_B0_C0

// 大整数 bigint
// const a = 2172141653n;
// const b = 15346349309n;
// BigInt 可以保持精度
// a * b // 33334444555566667777n
// 普通整数无法保持精度
// Number(a) * Number(b) // 33334444555566670000

// Error类 新加cause
//const actual = new Error('an error!', {cause: 'Error cause'});


// 判断对象是否是继承的·
// JavaScript 对象的属性分成两种：自身的属性和继承的属性。
// ES2022 在Object对象上面新增了一个静态方法Object.hasOwn()，也可以判断是否为自身的属性。

// 链判断运算符
// const firstName = message?.body?.user?.firstName || 'default';
// 三种写法
// obj?.prop // 对象属性是否存在
// obj?.[expr] // 同上
// a?.b()  函数或对象方法是否存在
// a?.() 函数
// 以下场合都失败 1.构造函数 2.链判断运算符的右侧有模板字符串 3.链判断运算符的左侧是 super 4.链运算符用于赋值运算符左侧


// 判断null和undefined ??
// 运算符左侧的值为null或undefined时，才会返回右侧的值。
// const headerText = response.settings.headerText ?? 'Hello, world!';

// synmbol
// 主要解决什么问题：
// 在JavaScript中，对象的属性名一般是字符串类型。当我们向一个对象添加新的属性时，有可能无意中覆盖原有的同名属性。这种情况在使用第三方库或者维护大型应用时尤为常见。因此，在这样的场景下，就需要一种机制来确保新增的属性名不会与现有的属性名产生冲突。
// Symbol.iterator 允许我们定义或修改一个对象的迭代行为。
//
// let s1 = Symbol('foo');
// let s2 = Symbol('foo');
// console.log(s1 === s2);  // false, 每个 Symbol 都是独一无二的



// 基本和对象有关，然后是临时存储对象，然后及时释放
// weakref >>  map weakmap
// set和 WeakSet最大区别： weakset 的成员只能是对象和 Symbol 值，而不能是其他类型的值。
// 使用场景WeakSet WeakMap  适合临时存放一组对象，以及存放跟对象绑定的信息。只要这些对象在外部消失，它在 WeakSet 里面的引用就会自动消失。
// WeakMap不适合遍历
//


// GC调用之后的hook FinalizationRegistry


// proxy 给原对象添加【扩展，操作变灵活】，原对象的元编程
// 就是任意访问对象之前的hook，其实就是代理对象,函数也是对象，所以自然而然有construct属性


// reflect 更简单的【操作，操作更简单】“原有对象”
// var myObject = Object.create(null) // 此时myObject并没有继承Object这个原型的任何方法,因此有：
// myObject.hasOwnProperty === undefined // 此时myObject是没有hasOwnProperty这个方法，那么我们要如何使用呢？如下：
// Object.prototype.hasOwnProperty.call(myObject, 'foo') // 是不是很恐怖，写这么一大串的代码！！！！
// 而如果使用Reflect对象呢？
// var myObject = Object.create(null)
// Reflect.ownKeys(myObject)
// const obj = {};
// Reflect.defineProperty(obj, 'name', {
//     value: 'John',
//     configurable: false
// });
// // 旧写法
// Object.defineProperty(obj, 'name', {
//     value: 'John',
//     configurable: false
// });



// class
class C {
    static a = 3;
    #brand = 4;//私有属性
    c = 5;
    f

    // 静态变量通过计算得到
    static y
    static z


    // 静态变量 生成 取决于其他，就是需要计算
    static {
        try {
            const obj = {y:1,z:5};
            this.y = obj.y;
            this.z = obj.z;
        }
        catch {
            this.y = 3
            this.z = 4
        }
    }

    constructor() {
        this.f ??= 'John';
    }
    static isC(obj) {
        if (#brand in obj) { //in
            // 私有属性 #brand 存在
            return true;
        } else {
            // 私有属性 #foo 不存在
            return false;
        }
    }

    // this的解决通过箭头函数
}

// 继承
class Point {}
class ColorPoint extends Point {
    // 父类所有的属性和方法，都会被子类继承，除了私有的属性和方法。
    // 父类的静态属性和静态方法，也会被子类继承

    constructor() {
        super(); // 调用父类的constructor(x, y)
    }
}

// await import()

// for await...of 不是并发，而是顺序await调用


// js buffer的操作 https://sindresorhus.com/blog/goodbye-nodejs-buffer 用uint8Array代替buffer






















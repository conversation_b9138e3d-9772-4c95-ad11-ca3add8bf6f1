<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta
      name="description"
      content="AI-powered chat agent built with Cloudflare Agents"
    />
    <meta name="theme-color" content="#000000" />

    <title>AI Chat Agent</title>

    <script>
      document.documentElement.classList.toggle(
        "dark",
        localStorage.theme === "dark" ||
          (!("theme" in localStorage) &&
            window.matchMedia("(prefers-color-scheme: dark)").matches)
      );
    </script>

    <!-- Favicon support -->
    <link rel="icon" href="/favicon.ico" />

    <!-- Preload fonts if any -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />

    <!-- Prevent flash of unstyled content -->
    <style>
      html {
        background: var(--background);
      }
      @media (prefers-color-scheme: dark) {
        html {
          background: var(--background);
        }
      }
    </style>
  </head>
  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="app"></div>
    <script type="module" src="/src/client.tsx"></script>
  </body>
</html>

@import "tailwindcss";

@custom-variant dark (&:where(.dark, .dark *));

:root {
  --background: oklch(1 0 0);
  --foreground: #1a1a1b;
  --card: oklch(1 0 0);
  --card-foreground: #1a1a1b;
  --popover: oklch(1 0 0);
  --popover-foreground: #1a1a1b;
  --primary: #f48120;
  --primary-foreground: oklch(0.985 0 0);
  --secondary: #faad3f;
  --secondary-foreground: #1a1a1b;
  --muted: oklch(0.97 0 0);
  --muted-foreground: #1a1a1b;
  --accent: #faad3f;
  --accent-foreground: #1a1a1b;
  --destructive: oklch(0.577 0.245 27.325);
  --destructive-foreground: oklch(0.577 0.245 27.325);
  --border: #faad3f;
  --assistant-border: #faad3f;
  --input: #faad3f;
  --input-background: oklch(0.97 0 0);
  --ring: #f48120;
  --chart-1: #f48120;
  --chart-2: #faad3f;
  --chart-3: #1a1a1b;
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --radius: 0.625rem;
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: #1a1a1b;
  --sidebar-primary: #f48120;
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: #faad3f;
  --sidebar-accent-foreground: #1a1a1b;
  --sidebar-border: #faad3f;
  --sidebar-ring: #f48120;
}

.dark {
  --background: #1a1a1b;
  --foreground: oklch(0.985 0 0);
  --card: #1a1a1b;
  --card-foreground: oklch(0.985 0 0);
  --popover: #1a1a1b;
  --popover-foreground: oklch(0.985 0 0);
  --primary: #f48120;
  --primary-foreground: oklch(0.985 0 0);
  --secondary: #faad3f;
  --secondary-foreground: oklch(0.985 0 0);
  --muted: #1a1a1b;
  --muted-foreground: #faad3f;
  --accent: #faad3f;
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.396 0.141 25.723);
  --destructive-foreground: oklch(0.637 0.237 25.331);
  --border: #faad3f;
  --assistant-border: #8b4d14;
  --input: #faad3f;
  --input-background: #0f0f10;
  --ring: #f48120;
  --chart-1: #f48120;
  --chart-2: #faad3f;
  --chart-3: #1a1a1b;
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: #1a1a1b;
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: #f48120;
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: #faad3f;
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: #faad3f;
  --sidebar-ring: #f48120;
}

@theme inline {
  --font-sans: var(--font-sans);
  --font-mono: var(--font-mono);
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-assistant-border: var(--assistant-border);
  --color-input: var(--input);
  --color-input-background: var(--input-background);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
  --animate-accordion-down: accordion-down 0.2s ease-out;
  --animate-accordion-up: accordion-up 0.2s ease-out;

  @keyframes accordion-down {
    from {
      height: 0;
    }
    to {
      height: var(--radix-accordion-content-height);
    }
  }

  @keyframes accordion-up {
    from {
      height: var(--radix-accordion-content-height);
    }
    to {
      height: 0;
    }
  }
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}

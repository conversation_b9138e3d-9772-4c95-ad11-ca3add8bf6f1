import {S3Client} from "bun";
import {readFile} from "fs/promises";




const client = new S3Client({
    accessKeyId: "3VIRF3TD5Y8RJS2UG9OT",
    secretAccessKey: "asWzBHdClr0CGSrY6Lt7HZBejplAyOe9E4qMGQX1",
    bucket: "dmail-internal-files",
    endpoint: "https://ap-south-1.linodeobjects.com",
});

// 2. 创建 sss.md 文件并写入 "hello"
// await client.write("sss.md", "内容1111222", {
//     type: "application/json",
//     acl: "public-read",
// });

let file = 'Soneium_Dmail_NFT_Bonus_Card.jpeg'
const imageBuffer = await readFile("/Users/<USER>/Downloads/" + file);
const a = await client.write(file, imageBuffer, {
    type:"image/jpeg",
    acl: "public-read",
});
console.log('a',a)



const g = await client.list()
console.log('g',g)

// 3. 读取 sss.md 文件内容
// const file = client.file("sss.md");
// const content = await file.text();

// console.log(content); // 输出: hello


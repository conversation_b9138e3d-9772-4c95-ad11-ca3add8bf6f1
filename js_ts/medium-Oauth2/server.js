require('dotenv').config(); // 加载 .env 文件中的环境变量
const express = require('express');
const axios = require('axios');
const path = require('path');

// 2. 初始化 Express 应用
const app = express();
const PORT = 3000;

// 3. 从环境变量中获取凭证
const MEDIUM_CLIENT_ID = process.env.MEDIUM_CLIENT_ID;
const MEDIUM_CLIENT_SECRET = process.env.MEDIUM_CLIENT_SECRET;
const CALLBACK_URL = process.env.CALLBACK_URL;

// 4. 设置静态文件目录，用于提供 HTML, CSS, 前端 JS 文件
app.use(express.static('public'));

// 5. 定义路由

// 路由一：重定向到 Medium 授权页面
app.get('/auth/medium', (req, res) => {
    // 构建 Medium 授权 URL
    const authUrl = new URL('https://medium.com/m/oauth/authorize');
    authUrl.searchParams.append('client_id', MEDIUM_CLIENT_ID);
    authUrl.searchParams.append('scope', 'basicProfile,listPublications'); // 请求权限范围
    authUrl.searchParams.append('state', 'your-random-state-string'); // 重要：用于防止 CSRF 攻击，生产环境应生成随机字符串
    authUrl.searchParams.append('response_type', 'code');
    authUrl.searchParams.append('redirect_uri', CALLBACK_URL);

    // 重定向用户
    res.redirect(authUrl.toString());
});

// 路由二：处理 Medium 的回调
app.get('/auth/medium/callback', async (req, res) => {
    const { code, state } = req.query;

    // 在生产环境中，您应该验证这里的 state 值与之前发送的是否一致
    if (!code) {
        return res.status(400).send('Error: No authorization code provided.');
    }

    try {
        // --- 第一步：用授权码(code)换取访问令牌(access_token) ---
        const tokenResponse = await axios.post('https://api.medium.com/v1/tokens', null, {
            params: {
                code: code,
                client_id: MEDIUM_CLIENT_ID,
                client_secret: MEDIUM_CLIENT_SECRET,
                grant_type: 'authorization_code',
                redirect_uri: CALLBACK_URL,
            }
        });

        const accessToken = tokenResponse.data.access_token;

        // --- 第二步：使用 access_token 获取用户信息 ---
        const userResponse = await axios.get('https://api.medium.com/v1/me', {
            headers: {
                'Authorization': `Bearer ${accessToken}`,
                'Content-Type': 'application/json',
                'Accept': 'application/json',
                'Accept-Charset': 'utf-8'
            }
        });

        const userData = userResponse.data.data;

        // --- 第三步：将用户信息传递给前端 ---
        // 为了演示简单，我们通过查询参数重定向到 profile 页面
        // 在生产应用中，更安全的做法是创建服务器端 session
        const profileUrl = new URL('/profile.html', `http://localhost:${PORT}`);
        profileUrl.searchParams.append('name', userData.name);
        profileUrl.searchParams.append('username', userData.username);
        profileUrl.searchParams.append('avatarUrl', userData.imageUrl);
        profileUrl.searchParams.append('profileUrl', userData.url);

        res.redirect(profileUrl.toString());

    } catch (error) {
        console.error('Error during authentication:', error.response ? error.response.data : error.message);
        res.status(500).send('Authentication failed.');
    }
});

// 6. 启动服务器
app.listen(PORT, () => {
    console.log(`Server is running at http://localhost:${PORT}`);
    console.log('Please open your browser and go to http://localhost:3000');
});
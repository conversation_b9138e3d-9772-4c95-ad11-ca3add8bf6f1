<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Your Profile</title>
    <style>
        body {
            font-family: sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            flex-direction: column;
            text-align: center;
        }

        #avatar {
            border-radius: 50%;
            width: 150px;
            height: 150px;
            margin-bottom: 20px;
        }

        #profile-link {
            color: #00ab6c;
        }
    </style>
</head>

<body>
    <div id="profile-container">
        <h1 id="welcome-message"></h1>
        <img id="avatar" src="" alt="User Avatar">
        <p>Username: <strong id="username"></strong></p>
        <a id="profile-link" href="#" target="_blank">View on Medium</a>
    </div>

    <script>
        // 当页面加载完成后执行
        window.onload = function () {
            // 从 URL 查询参数中获取用户信息
            const params = new URLSearchParams(window.location.search);
            const name = params.get('name');
            const username = params.get('username');
            const avatarUrl = params.get('avatarUrl');
            const profileUrl = params.get('profileUrl');

            if (name) {
                // 更新页面元素
                document.getElementById('welcome-message').textContent = `Welcome, ${name}!`;
                document.getElementById('avatar').src = avatarUrl;
                document.getElementById('username').textContent = username;
                document.getElementById('profile-link').href = profileUrl;
            } else {
                // 如果没有用户信息，显示错误
                document.getElementById('profile-container').innerHTML = '<h1>Could not load user profile. Please try logging in again.</h1>';
            }
        };
    </script>
</body>

</html>
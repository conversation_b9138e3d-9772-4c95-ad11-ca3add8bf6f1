const async_hooks = require('async_hooks');
const http = require('http');

// 创建一个 Map 来存储每个异步操作的开始时间。
const asyncStartTime = new Map();

// 创建一个 AsyncHook 实例。
const asyncHook = async_hooks.createHook({
    init(asyncId, type, triggerAsyncId, resource) {
        // 记录异步操作的开始时间。
        // console.log('type', type)
        // console.log('resource', resource)
        asyncStartTime.set(asyncId, Date.now());
    },
    destroy(asyncId) {
        // 计算异步操作的执行时间。
        const startTime = asyncStartTime.get(asyncId);
        if (startTime) {
            const endTime = Date.now();
            const duration = endTime - startTime;
            console.log(`Async operation ${asyncId}  took ${duration}ms`);
            asyncStartTime.delete(asyncId);
        }
    },
});

// 启用 AsyncHook。
asyncHook.enable();

const a = cb => cb()
let d = a(() => 3)
console.log('d',d)


import { x25519 } from '@noble/curves/ed25519';
import { randomBytes, createCipheriv, createDecipheriv } from 'crypto';

// 生成随机 AES 密钥
function generateAesKey() {
    return randomBytes(32); // 256-bit AES key
}

// 使用 x25519 公钥加密 AES 密钥
function encryptAesKeyWithX25519(aesKey, publicKey) {
    const ephemeralPrivateKey = x25519.utils.randomPrivateKey(); // 临时私钥
    const ephemeralPublicKey = x25519.getPublicKey(ephemeralPrivateKey); // 临时公钥

    // 计算共享密钥
    const sharedSecret = x25519.getSharedSecret(ephemeralPrivateKey, publicKey);

    // 使用共享密钥加密 AES 密钥
    const cipher = createCipheriv('aes-256-gcm', sharedSecret.slice(0, 32), sharedSecret.slice(32, 48));
    const encryptedAesKey = Buffer.concat([cipher.update(aesKey), cipher.final()]);

    return {
        ephemeralPublicKey, // 发送临时公钥
        encryptedAesKey, // 发送加密后的 AES 密钥
        authTag: cipher.getAuthTag(), // GCM 认证标签
    };
}

// 使用 x25519 私钥解密 AES 密钥
function decryptAesKeyWithX25519(encryptedAesKey, ephemeralPublicKey, privateKey, authTag) {
    // 计算共享密钥
    const sharedSecret = x25519.getSharedSecret(privateKey, ephemeralPublicKey);

    // 使用共享密钥解密 AES 密钥
    const decipher = createDecipheriv('aes-256-gcm', sharedSecret.slice(0, 32), sharedSecret.slice(32, 48));
    decipher.setAuthTag(authTag); // 设置认证标签
    const aesKey = Buffer.concat([decipher.update(encryptedAesKey), decipher.final()]);

    return aesKey;
}

// 使用 AES 加密数据
function encryptWithAes(data, aesKey) {
    const iv = randomBytes(12); // 12-byte IV for GCM
    const cipher = createCipheriv('aes-256-gcm', aesKey, iv);
    const encryptedData = Buffer.concat([cipher.update(data), cipher.final()]);
    return {
        iv, // 发送 IV
        encryptedData, // 发送加密后的数据
        authTag: cipher.getAuthTag(), // GCM 认证标签
    };
}

// 使用 AES 解密数据
function decryptWithAes(encryptedData, aesKey, iv, authTag) {
    const decipher = createDecipheriv('aes-256-gcm', aesKey, iv);
    decipher.setAuthTag(authTag); // 设置认证标签
    const data = Buffer.concat([decipher.update(encryptedData), decipher.final()]);
    return data;
}

// 示例
(async () => {
    // Alice 生成密钥对
    const alicePrivateKey = x25519.utils.randomPrivateKey();
    const alicePublicKey = x25519.getPublicKey(alicePrivateKey);

    // Bob 生成密钥对
    const bobPrivateKey = x25519.utils.randomPrivateKey();
    const bobPublicKey = x25519.getPublicKey(bobPrivateKey);

    // Alice 生成随机 AES 密钥
    const aesKey = generateAesKey();
    console.log('AES Key:', aesKey.toString('hex'));

    // Alice 使用 Bob 的公钥加密 AES 密钥
    const { ephemeralPublicKey, encryptedAesKey, authTag } = encryptAesKeyWithX25519(aesKey, bobPublicKey);
    console.log('Encrypted AES Key:', encryptedAesKey.toString('hex'));

    // Alice 使用 AES 加密数据
    const data = Buffer.from('Hello, world!');
    const { iv, encryptedData, authTag: dataAuthTag } = encryptWithAes(data, aesKey);
    console.log('Encrypted Data:', encryptedData.toString('hex'));

    // Bob 使用自己的私钥解密 AES 密钥
    const decryptedAesKey = decryptAesKeyWithX25519(encryptedAesKey, ephemeralPublicKey, bobPrivateKey, authTag);
    console.log('Decrypted AES Key:', decryptedAesKey.toString('hex'));

    // Bob 使用 AES 解密数据
    const decryptedData = decryptWithAes(encryptedData, decryptedAesKey, iv, dataAuthTag);
    console.log('Decrypted Data:', decryptedData.toString());
})();
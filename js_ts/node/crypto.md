createDiffieHellman(2048) 是基于经典的 Diffie-Hellman 密钥交换算法（DH），使用的是模幂运算（modular exponentiation），而不是椭圆曲线密码学（ECC）。具体来说，它会生成一个 2048 位的 DH 密钥对，基于大素数和生成元（generator）。这与 X25519 无关。

X25519也是基于DiffieHellman




验证
前端生成alice密钥队，
后端生成bob密钥队
然后这两的sharekey相同

共享出来的密钥还不能直接用，最好还是需要需要hash+salt


详细告诉我这个类型
   type KeyUsage =
            | "decrypt"
            | "deriveBits"
            | "deriveKey"
            | "encrypt"
            | "sign"
            | "unwrapKey"
            | "verify"
            | "wrapKey";

每个字符串的作用并给出demo
keyObject js的加密keyring类型

deriveBits 派生生成的是arrayview 二进制，要经过 crypto.createHmac('sha256', 'salt') 才能变成一个
deriveKey 派生生成的是keyObject 直接密钥
sign 和 verify 搭配使用签名和验证 x25519不行 ed25519可以
wrapKey和unwrapKey搭配使用，包装密钥



# HMAC
HMAC（Hash-based Message Authentication Code，基于哈希的消息认证码）是一种用于验证消息完整性和真实性的加密技术。它结合了哈希函数（如 SHA-256、MD5）和密钥，生成一个固定长度的认证码（MAC），用于验证消息是否被篡改或伪造。
​HMAC 是一种基于哈希函数的消息认证码，用于验证数据的完整性和真实性。
​SHA256 是一种安全的哈希函数，输出长度为 32 字节。
​Salt 是一个随机值，用于增加密钥派生的随机性和安全性。
通过 HMAC-SHA256，可以将共享密钥和盐值结合，生成一个适合加密的派生密钥。

肯定不是长期的（因为怕被刷而且要求公钥不变），定期更换前端的密钥且写使，aes
x25519 + AES 的最佳实践包括：
​直接使用共享密钥：适合双向通信和长期通信。

​加密 AES 密钥：适合单向通信或需要频繁更换密钥的场景。
如果是aes加密 公钥

​加密 AES 密钥模式更接近 RSA + AES 的实现，适合需要显式发送密钥的场景。

2天换4次密钥


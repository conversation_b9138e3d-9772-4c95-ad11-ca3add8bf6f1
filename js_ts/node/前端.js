// import crypto from 'crypto'
const crypto = require('crypto')

async function generateKeyPair() {
    return await crypto.subtle.generateKey(
        {
            name: 'X25519',
        },
        true, // 是否可导出
        ['deriveKey', 'deriveBits'] // 密钥用途–
    );
}

// 2. 派生共享密钥
async function deriveSharedSecret(privateKey, publicKey) {
    return await crypto.subtle.deriveKey(
        {
            name: 'X25519',
            public: publicKey,
        },
        privateKey,
        {
            name: 'AES-GCM',
            length: 256, // 256位 AES 密钥
        },
        true, // 是否可导出
        ['encrypt', 'decrypt'] // 密钥用途
    );
}

// 3. 加密数据
async function encryptData(key, plaintext) {
    const nonce = crypto.getRandomValues(new Uint8Array(12)); // 12字节 Nonce
    const encodedPlaintext = new TextEncoder().encode(plaintext); // 明文编码为 Uint8Array

    const encrypted = await crypto.subtle.encrypt(
        {
            name: 'AES-GCM',
            iv: nonce,
        },
        key,
        encodedPlaintext
    );

    return {
        encryptedData: new Uint8Array(encrypted),
        nonce,
    };
}

// 4. 解密数据
async function decryptData(key, encryptedData, nonce) {
    const decrypted = await crypto.subtle.decrypt(
        {
            name: 'AES-GCM',
            iv: nonce,
        },
        key,
        encryptedData
    );

    return new TextDecoder().decode(decrypted); // 解码为字符串
}

async function printSharedKey(sharedKey, type) {
    if (!sharedKey.extractable) {
        console.log('Key is not extractable.');
        return;
    }

    // 导出为原始字节数组
    const rawKey = await crypto.subtle.exportKey('raw', sharedKey);
    const rawKeyArray = new Uint8Array(rawKey);

    // 打印原始字节数组
    // console.log('Raw Shared Key:',type, rawKeyArray);
    console.log('Hex Shared Key:', type, Array.from(rawKeyArray).map(b => b.toString(16).padStart(2, '0')).join(''));
}

async function importPrivateKey(rawKey) {
    return await crypto.subtle.importKey(
        'raw', // 密钥格式
        rawKey, // 原始密钥数据
        {
            name: 'X25519',
        },
        true, // 是否可导出
        ['deriveKey', 'deriveBits'] // 密钥用途
    );
}

// 5. 主流程
async function main() {
    // Alice 生成密钥对
    const aliceKeyPair = await generateKeyPair();

    // Bob 生成密钥对
    const bobKeyPair = await generateKeyPair();
    // const bobPrivateKeyRaw = new Uint8Array([...]);   // 32字节私钥
    // const bobKeyPair = await importPrivateKey(bobPrivateKeyRaw)


    // Alice 计算共享密钥
    const aliceSharedKey = await deriveSharedSecret(aliceKeyPair.privateKey, bobKeyPair.publicKey);
    printSharedKey(aliceSharedKey, 'aliceSharedKey')

    const aliceSharedKey2 = await deriveSharedSecret(aliceKeyPair.privateKey, bobKeyPair.publicKey);
    printSharedKey(aliceSharedKey2, 'aliceSharedKey2')

    // Bob 计算共享密钥
    const bobSharedKey = await deriveSharedSecret(bobKeyPair.privateKey, aliceKeyPair.publicKey);
    printSharedKey(bobSharedKey, 'bobSharedKey')

    // Alice 加密数据
    const plaintext = 'Hello, this is a secret message!';
    const {encryptedData, nonce} = await encryptData(aliceSharedKey, plaintext);

    // Bob 解密数据
    const decrypted = await decryptData(bobSharedKey, encryptedData, nonce);

    // 输出结果
    console.log('明文:', plaintext);
    console.log('加密后:', new Uint8Array(encryptedData));
    console.log('解密后:', decrypted);
}

// 执行主流程
main().catch(console.error);
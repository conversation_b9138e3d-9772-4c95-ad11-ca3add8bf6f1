// const { mboxReader } = require('mbox-reader');
import fs from 'fs';
import {mboxReader} from 'mbox-reader';


const stream = fs.createReadStream('./All mail Including Spam and Trash.mbox');

const main1 = async () => {
    for await (let message of mboxReader(stream)) {
        // console.log(message.returnPath);
        console.log(message.content);
        // process.stdout.write(message.content);
    }

}
main1()

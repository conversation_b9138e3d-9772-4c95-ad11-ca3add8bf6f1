import fs from "node:fs";

// ============= 类型定义 =============
interface ParsedEmail {
    subject: string | null;
    html: string | null;
    htmlHeaders: Record<string, string> | null;
    attachment: any[];
    from: string | null;
    to: string | null;
    datetime: Date | null;
}

interface EmailPart {
    headers: Record<string, string>;
    body: string;
    isMultipart: boolean;
    boundary: string | null;
    contentType: string;
    contentDisposition: string | null;
}

// 使用流式处理大文件（特别是mbox）
async function parseLargeMbox(filePath: string): Promise<ParsedEmail[]> {
    const emails: ParsedEmail[] = [];
    const stream = fs.createReadStream(filePath, {encoding: 'utf8'});
    let buffer = '';

    return new Promise((resolve, reject) => {
        stream.on('data', (chunk) => {
            buffer += chunk;
            const emailsInBuffer = splitMboxContent(buffer);

            // 处理完整邮件，保留不完整的部分
            if (emailsInBuffer.length > 1) {
                emails.push(...emailsInBuffer.slice(0, -1).map(parseEmail));
                buffer = emailsInBuffer[emailsInBuffer.length - 1];
            }
        });

        stream.on('end', () => {
            if (buffer.trim()) {
                emails.push(parseEmail(buffer));
            }
            resolve(emails);
        });

        stream.on('error', reject);
    });
}

// 预编译常用正则表达式
const BOUNDARY_REGEX = /boundary=(?:"([^"]+)"|([^;\s]+))/i;
const FROM_LINE_REGEX = /^From .+$/gm;
const ENCODED_SUBJECT_REGEX = /=\?([^?]+)\?([BQ])\?(.*?)\?=/gi;
const EMAIL_REGEX = /\b[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}\b/;
const HEADER_SPLIT_REGEX = /\r?\n\r?\n/;

// 在函数中使用预编译的正则
function extractBoundary(contentType: string): string | null {
    const boundaryMatch = BOUNDARY_REGEX.exec(contentType);
    return boundaryMatch ? (boundaryMatch[1] || boundaryMatch[2]) : null;
}

// 优化 parseHeaders 函数
function parseHeaders(headerString: string): Record<string, string> {
    const headers: Record<string, string> = {};
    let currentPos = 0;
    const length = headerString.length;

    while (currentPos < length) {
        const colonIndex = headerString.indexOf(':', currentPos);
        if (colonIndex === -1) break;

        const lineEnd = headerString.indexOf('\n', colonIndex);
        const key = headerString.slice(currentPos, colonIndex).toLowerCase().trim();

        let valueStart = colonIndex + 1;
        // 跳过空格
        while (valueStart < length && (headerString[valueStart] === ' ' || headerString[valueStart] === '\t')) {
            valueStart++;
        }

        const value = lineEnd === -1
            ? headerString.slice(valueStart).trim()
            : headerString.slice(valueStart, lineEnd).trim();

        headers[key] = value;
        currentPos = lineEnd === -1 ? length : lineEnd + 1;
    }

    return headers;
}

// ============= 公共API函数 =============
/**
 * 自动检测并解析邮件格式（支持mbox和eml）
 */
function parseEmailFile(content: string, filename?: string): ParsedEmail[] {
    if (isEmlFormat(content, filename)) {
        return [parseEml(content)];
    } else {
        return parseMbox(content);
    }
}
/**
 * 解析EML格式邮件（单个邮件文件）
 */
function parseEml(emlString: string): ParsedEmail {
    return parseEmail(emlString);
}

/**
 * 解析mbox格式邮件（多个邮件的集合）
 */
function parseMbox(mboxString: string): ParsedEmail[] {
    return splitMboxContent(mboxString).map(parseEmail);
}
/**
 * 自动检测并解析邮件格式（支持mbox和eml）
 */
function parseEmailFile(content: string, filename?: string): ParsedEmail[] {
    if (isEmlFormat(content, filename)) {
        return [parseEml(content)];
    } else {
        return parseMbox(content);
    }
}



// 分批处理大mbox文件
async function parseMboxInBatches(mboxString: string, batchSize = 10): Promise<ParsedEmail[]> {
    const emailStrings = splitMboxContent(mboxString);
    const results: ParsedEmail[] = [];

    for (let i = 0; i < emailStrings.length; i += batchSize) {
        const batch = emailStrings.slice(i, i + batchSize);
        const parsedBatch = batch.map(parseEmail);
        results.push(...parsedBatch);

        // 强制垃圾回收（Node.js中需要特殊处理）
        if (global.gc) {
            global.gc();
        }

        // 给事件循环喘息的机会
        await new Promise(resolve => setImmediate(resolve));
    }

    return results;
}


// 添加简单的缓存机制
const boundaryCache = new Map<string, string | null>();

function extractBoundaryWithCache(contentType: string): string | null {
    if (boundaryCache.has(contentType)) {
        return boundaryCache.get(contentType)!;
    }

    const boundary = extractBoundary(contentType);
    boundaryCache.set(contentType, boundary);
    return boundary;
}


// 对于多核CPU，可以考虑并行处理
async function parseEmailsParallel(emailStrings: string[]): Promise<ParsedEmail[]> {
    // 使用worker threads或child processes进行并行处理
    // 注意：这适用于大量邮件的场景，小批量可能反而更慢
    return Promise.all(emailStrings.map(async (emailStr) => {
        return parseEmail(emailStr);
    }));
}

// 避免将大附件完全加载到内存中
function processLargeAttachment(part: EmailPart, outputDir: string): any {
    const filename = extractFilename(part.contentDisposition || '');
    const outputPath = path.join(outputDir, filename);

    // 直接流式写入文件
    const writeStream = fs.createWriteStream(outputPath);
    const content = decodeContent(part.body, part.headers['content-transfer-encoding'] || '');

    return new Promise((resolve) => {
        writeStream.write(Buffer.from(content, 'binary'));
        writeStream.end(() => {
            resolve({
                "content-Type": part.contentType.split(';')[0].trim(),
                "filename": filename,
                "filePath": outputPath, // 返回文件路径而不是base64内容
                "size": content.length
            });
        });
    });
}

const main = async () => {
    try {
        const testFiles = [
            './222.eml',
            './All mail Including Spam and Trash.mbox',
        ];

        for (const filePath of testFiles) {
            if (!fs.existsSync(filePath)) {
                console.log(`File not found: ${filePath}, skipping...`);
                continue;
            }

            console.log(`\nProcessing file: ${filePath}`);

            // 根据文件大小选择处理方式
            const stats = fs.statSync(filePath);
            let parsedEmails: ParsedEmail[];

            if (stats.size > 10 * 1024 * 1024) { // 大于10MB
                parsedEmails = await parseLargeMbox(filePath);
            } else {
                const content = fs.readFileSync(filePath, "utf-8");
                parsedEmails = parseEmailFile(content, filePath);
            }

            const outputFile = filePath.replace(/\.(eml|mbox)$/, '_parsed.json');
            fs.writeFileSync(outputFile, JSON.stringify(parsedEmails, null, 2));

            console.log(`Done! Processed ${parsedEmails.length} emails -> ${outputFile}`);
        }
    } catch (error) {
        console.error('Error processing email files:', error);
    }
};
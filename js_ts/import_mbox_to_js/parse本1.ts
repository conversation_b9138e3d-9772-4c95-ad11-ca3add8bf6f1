import fs from "node:fs";
import { createReadStream } from "node:fs";
import { createInterface } from "node:readline";

// ============= 类型定义 =============
interface ParsedEmail {
    subject: string | null;
    html: string | null;
    attachment: any[];
    from: string | null;
    to: string | null;
    datetime: Date | null;
}

interface EmailPart {
    headers: Record<string, string>;
    body: string;
    isMultipart: boolean;
    boundary: string | null;
    contentType: string;
    contentDisposition: string | null;
}

interface StreamParseOptions {
    chunkSize?: number;
    maxEmailSize?: number;
    onEmailParsed?: (email: ParsedEmail, index: number) => void;
    onProgress?: (processed: number, total?: number) => void;
}

// ============= 核心解析函数 =============

/**
 * 优化的邮件头部解析 - 减少正则表达式使用
 */
function parseHeaders(headerString: string): Record<string, string> {
    const headers: Record<string, string> = {};

    // 手动处理折叠行，避免正则表达式
    let unfoldedHeaders = '';
    const lines = headerString.split('\n');

    for (let i = 0; i < lines.length; i++) {
        let line = lines[i];
        if (line.endsWith('\r')) {
            line = line.slice(0, -1);
        }

        // 检查下一行是否是折叠行
        while (i + 1 < lines.length) {
            const nextLine = lines[i + 1];
            if (nextLine.startsWith(' ') || nextLine.startsWith('\t')) {
                line += ' ' + nextLine.trim();
                i++;
            } else {
                break;
            }
        }

        unfoldedHeaders += line + '\n';
    }

    // 解析头部字段
    const headerLines = unfoldedHeaders.split('\n');
    for (const line of headerLines) {
        const colonIndex = line.indexOf(':');
        if (colonIndex > 0) {
            const key = line.slice(0, colonIndex).toLowerCase().trim();
            const value = line.slice(colonIndex + 1).trim();
            if (key && value) {
                headers[key] = value;
            }
        }
    }
    return headers;
}

/**
 * 优化的边界提取 - 手动解析避免正则表达式
 */
function extractBoundary(contentType: string): string | null {
    const lowerContentType = contentType.toLowerCase();
    const boundaryIndex = lowerContentType.indexOf('boundary=');
    if (boundaryIndex === -1) return null;

    let start = boundaryIndex + 9; // 'boundary='.length
    let boundary = '';

    // 跳过可能的引号
    if (contentType[start] === '"') {
        start++;
        const endQuote = contentType.indexOf('"', start);
        if (endQuote !== -1) {
            boundary = contentType.slice(start, endQuote);
        }
    } else {
        // 找到分号或字符串结尾
        let end = start;
        while (end < contentType.length &&
               contentType[end] !== ';' &&
               contentType[end] !== ' ' &&
               contentType[end] !== '\t') {
            end++;
        }
        boundary = contentType.slice(start, end);
    }

    return boundary || null;
}

/**
 * 解析单个邮件部分
 */
function parseEmailPart(emailPartString: string): EmailPart {
    const headerBodyMatch = emailPartString.match(/^([\s\S]*?)\r?\n\r?\n([\s\S]*)$/);
    if (!headerBodyMatch) {
        return {
            headers: {},
            body: emailPartString,
            isMultipart: false,
            boundary: null,
            contentType: 'text/plain',
            contentDisposition: null,
        };
    }

    const [, headerString, body] = headerBodyMatch;
    const headers = parseHeaders(headerString);
    const contentType = headers['content-type'] || 'text/plain';
    const isMultipart = contentType.startsWith('multipart/');
    const boundary = isMultipart ? extractBoundary(contentType) : null;

    return {
        headers,
        body,
        isMultipart,
        boundary,
        contentType,
        contentDisposition: headers['content-disposition'],
    };
}

// ============= 内容解码函数 =============

/**
 * 解码quoted-printable内容 - 优化版本支持UTF-8
 */
function decodeQuotedPrintable(content: string): string {
    // 首先移除软换行
    let decoded = content.replace(/=\r?\n/g, '');

    // 收集所有的十六进制字节
    const hexBytes: number[] = [];
    let result = '';
    let i = 0;

    while (i < decoded.length) {
        if (decoded[i] === '=' && i + 2 < decoded.length) {
            const hex = decoded.substring(i + 1, i + 3);
            if (/^[0-9A-F]{2}$/i.test(hex)) {
                hexBytes.push(parseInt(hex, 16));
                i += 3;
            } else {
                // 如果不是有效的十六进制，保持原样
                result += decoded[i];
                i++;
            }
        } else {
            // 如果有累积的字节，先处理它们
            if (hexBytes.length > 0) {
                try {
                    // 将字节数组转换为UTF-8字符串
                    const buffer = Buffer.from(hexBytes);
                    result += buffer.toString('utf8');
                } catch (e) {
                    // 如果UTF-8解码失败，逐个字符处理
                    result += hexBytes.map(b => String.fromCharCode(b)).join('');
                }
                hexBytes.length = 0;
            }

            result += decoded[i];
            i++;
        }
    }

    // 处理剩余的字节
    if (hexBytes.length > 0) {
        try {
            const buffer = Buffer.from(hexBytes);
            result += buffer.toString('utf8');
        } catch (e) {
            result += hexBytes.map(b => String.fromCharCode(b)).join('');
        }
    }

    return result;
}

/**
 * 解码base64内容
 */
function decodeBase64(content: string): string {
    try {
        const cleanBase64 = content.replace(/\s/g, '');
        return Buffer.from(cleanBase64, 'base64').toString('utf8');
    } catch (e) {
        console.warn('Failed to decode base64 content:', e);
        return content;
    }
}

/**
 * 根据编码类型解码内容
 */
function decodeContent(content: string, encoding: string): string {
    if (!content) return '';

    switch (encoding?.toLowerCase()) {
        case 'quoted-printable':
            return decodeQuotedPrintable(content);
        case 'base64':
            return decodeBase64(content);
        case '7bit':
        case '8bit':
        case 'binary':
        default:
            return content;
    }
}

/**
 * 修复HTML内容中的常见编码问题
 */
function fixHtmlEncoding(html: string): string {
    if (!html) return html;

    // 修复常见的UTF-8字符编码问题
    const fixes: Array<[RegExp, string | ((match: string, hex: string) => string)]> = [
        // 不间断空格
        [/=C2=A0/g, '\u00A0'],
        // 其他常见的UTF-8字符
        [/=E2=80=99/g, '\u2019'], // 右单引号
        [/=E2=80=98/g, '\u2018'], // 左单引号
        [/=E2=80=9C/g, '\u201C'], // 左双引号
        [/=E2=80=9D/g, '\u201D'], // 右双引号
        [/=E2=80=93/g, '\u2013'], // en dash
        [/=E2=80=94/g, '\u2014'], // em dash
        [/=E2=80=A6/g, '\u2026'], // 省略号
        // emoji和特殊字符的常见编码
        [/=F0=9F=98=80/g, '\uD83D\uDE00'], // 笑脸
        [/=F0=9F=98=82/g, '\uD83D\uDE02'], // 笑哭
        [/=F0=9F=98=8D/g, '\uD83D\uDE0D'], // 心形眼
        [/=F0=9F=91=8D/g, '\uD83D\uDC4D'], // 点赞
        [/=F0=9F=91=8E/g, '\uD83D\uDC4E'], // 点踩
        [/=F0=9F=92=AF/g, '\uD83D\uDCAF'], // 100分
        // 处理剩余的quoted-printable编码
        [/=([0-9A-F]{2})/gi, (match: string, hex: string) => {
            try {
                return String.fromCharCode(parseInt(hex, 16));
            } catch (e) {
                return match;
            }
        }]
    ];

    let result = html;
    for (const [pattern, replacement] of fixes) {
        result = result.replace(pattern, replacement as string);
    }

    return result;
}



// ============= 内容处理函数 =============

/**
 * 处理HTML内容
 */
function processHtmlPart(part: EmailPart): string {
    const encoding = part.headers['content-transfer-encoding'];
    let decodedHtml = decodeContent(part.body, encoding).trim();

    // 应用HTML编码修复
    decodedHtml = fixHtmlEncoding(decodedHtml);

    return decodedHtml;
}

/**
 * 提取附件文件名
 */
function extractFilename(contentDisposition: string): string {
    const filenameMatch = contentDisposition.match(/filename\*?=(?:"([^"]+)"|([^;\s]+))/i);
    if (!filenameMatch) return 'unnamed';

    let filename = filenameMatch[1] || filenameMatch[2];
    // 处理RFC 2231编码的文件名
    if (filename.includes("''")) {
        filename = decodeURIComponent(filename.split("''")[1]);
    }
    return filename;
}

/**
 * 处理附件内容
 */
function processAttachmentPart(part: EmailPart): any {
    const filename = extractFilename(part.contentDisposition || '');
    const originalContentType = part.contentType.split(';')[0].trim();
    const encoding = part.headers['content-transfer-encoding']?.toLowerCase();

    let content = part.body;
    if (encoding === 'base64') {
        content = content.replace(/\s/g, '');
    } else {
        const decodedContent = decodeContent(content, encoding || '');
        content = Buffer.from(decodedContent, 'binary').toString('base64');
    }

    return {
        "content-Type": originalContentType,
        "filename": filename,
        "base64": content
    };
}

/**
 * 优化的multipart内容分割 - 手动查找边界避免正则表达式
 */
function splitMultipartContent(body: string, boundary: string): string[] {
    const parts: string[] = [];
    const boundaryMarker = `--${boundary}`;
    const endBoundaryMarker = `--${boundary}--`;

    let currentIndex = 0;
    let partStart = -1;

    // 手动查找边界标记
    while (currentIndex < body.length) {
        const lineStart = currentIndex;
        let lineEnd = body.indexOf('\n', currentIndex);
        if (lineEnd === -1) lineEnd = body.length;

        const line = body.slice(lineStart, lineEnd).trim();

        if (line === boundaryMarker || line === endBoundaryMarker) {
            // 找到边界标记
            if (partStart !== -1) {
                // 提取前一个部分
                const partContent = body.slice(partStart, lineStart).trim();
                if (partContent) {
                    parts.push(partContent);
                }
            }

            if (line === endBoundaryMarker) {
                // 结束标记，停止处理
                break;
            }

            partStart = lineEnd + 1;
        }

        currentIndex = lineEnd + 1;
    }

    // 处理最后一个部分
    if (partStart !== -1 && partStart < body.length) {
        const lastPart = body.slice(partStart).trim();
        if (lastPart && !lastPart.startsWith('--')) {
            parts.push(lastPart);
        }
    }

    return parts;
}

/**
 * 处理邮件正文内容
 */
function processBody(part: EmailPart): { html: string | null; attachments: any[] } {
    let html: string | null = null;
    let attachments: any[] = [];

    try {
        if (!part?.body) return { html, attachments };

        if (part.isMultipart && part.boundary) {
            // 处理multipart内容
            const parts = splitMultipartContent(part.body, part.boundary);

            for (const partContent of parts) {
                try {
                    const subPart = parseEmailPart(partContent);
                    const result = processBody(subPart);

                    if (result.html) html = result.html;
                    if (result.attachments.length) attachments.push(...result.attachments);
                } catch (err) {
                    console.warn('Failed to process email part:', err);
                }
            }
        } else if (part.contentType.includes('text/html') &&
            (!part.contentDisposition || !part.contentDisposition.includes('attachment'))) {
            // 处理HTML内容
            html = processHtmlPart(part);
        } else if (part.contentDisposition?.includes('attachment')) {
            // 处理附件
            attachments.push(processAttachmentPart(part));
        }
    } catch (err) {
        console.warn('Error in processBody:', err);
    }

    return { html, attachments };
}


// ============= 邮件字段处理函数 =============

/**
 * 解码邮件主题（RFC 2047）- 优化版本支持UTF-8
 */
function decodeEmailSubject(subject: string): string {
    if (!subject) return subject;

    return subject.replace(/=\?([^?]+)\?([BQ])\?(.*?)\?=/gi, (match, _charset, encoding, encodedText) => {
        try {
            if (encoding.toUpperCase() === 'B') {
                // Base64解码
                return Buffer.from(encodedText, 'base64').toString('utf8');
            } else if (encoding.toUpperCase() === 'Q') {
                // Quoted-printable解码，特别处理下划线
                const withSpaces = encodedText.replace(/_/g, ' ');

                // 使用改进的quoted-printable解码
                const hexBytes: number[] = [];
                let result = '';
                let i = 0;

                while (i < withSpaces.length) {
                    if (withSpaces[i] === '=' && i + 2 < withSpaces.length) {
                        const hex = withSpaces.substring(i + 1, i + 3);
                        if (/^[0-9A-F]{2}$/i.test(hex)) {
                            hexBytes.push(parseInt(hex, 16));
                            i += 3;
                        } else {
                            result += withSpaces[i];
                            i++;
                        }
                    } else {
                        // 处理累积的字节
                        if (hexBytes.length > 0) {
                            try {
                                const buffer = Buffer.from(hexBytes);
                                result += buffer.toString('utf8');
                            } catch (e) {
                                result += hexBytes.map(b => String.fromCharCode(b)).join('');
                            }
                            hexBytes.length = 0;
                        }

                        result += withSpaces[i];
                        i++;
                    }
                }

                // 处理剩余字节
                if (hexBytes.length > 0) {
                    try {
                        const buffer = Buffer.from(hexBytes);
                        result += buffer.toString('utf8');
                    } catch (e) {
                        result += hexBytes.map(b => String.fromCharCode(b)).join('');
                    }
                }

                return result;
            }
            return match;
        } catch (e) {
            console.warn('Failed to decode subject:', e);
            return match;
        }
    });
}

/**
 * 提取邮件地址
 */
function extractEmail(fromField: string): string | null {
    if (!fromField) return null;

    // 处理 "Name <<EMAIL>>" 格式
    const angleMatch = fromField.match(/<([^>]+)>/);
    if (angleMatch) return angleMatch[1].trim();

    // 直接匹配邮件地址
    const emailMatch = fromField.match(/\b[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}\b/);
    return emailMatch ? emailMatch[0] : null;
}

/**
 * 安全解析日期
 */
function parseEmailDate(dateString: string): Date | null {
    if (!dateString) return null;

    try {
        const date = new Date(dateString);
        return isNaN(date.getTime()) ? null : date;
    } catch (e) {
        console.warn('Invalid date format:', dateString);
        return null;
    }
}

// ============= 主要解析函数 =============

/**
 * 解析单个邮件内容为标准格式
 */
function parseEmail(emailString: string): ParsedEmail {
    try {
        const mainPart = parseEmailPart(emailString);
        const { html, attachments } = processBody(mainPart);

        return {
            subject: mainPart.headers.subject ? decodeEmailSubject(mainPart.headers.subject) : null,
            html: html || null,
            attachment: attachments,
            from: extractEmail(mainPart.headers.from || ''),
            to: extractEmail(mainPart.headers.to || ''),
            datetime: parseEmailDate(mainPart.headers.date || ''),
        };
    } catch (err) {
        console.warn('Failed to parse email:', err);
        return {
            subject: null,
            html: null,
            attachment: [],
            from: null,
            to: null,
            datetime: null,
        };
    }
}

// ============= 格式检测和分割函数 =============

/**
 * 检测是否为EML格式
 */
function isEmlFormat(content: string, filename?: string): boolean {
    return filename?.toLowerCase().endsWith('.eml') ||
        (!content.includes('\nFrom ') &&
            (content.includes('Message-ID:') || content.includes('Date:') || content.includes('Subject:')));
}

/**
 * 优化的mbox内容分割 - 手动查找From行
 */
function splitMboxContent(mboxString: string): string[] {
    const emails: string[] = [];
    const lines = mboxString.split('\n');
    let currentEmail: string[] = [];
    let inEmail = false;

    for (let i = 0; i < lines.length; i++) {
        const line = lines[i];

        // 检查是否是From行（mbox格式的邮件分隔符）
        if (line.startsWith('From ') && (i === 0 || lines[i-1] === '')) {
            // 保存前一个邮件
            if (inEmail && currentEmail.length > 0) {
                const emailContent = currentEmail.join('\n').trim();
                if (emailContent) {
                    emails.push(emailContent);
                }
            }

            // 开始新邮件
            currentEmail = [line];
            inEmail = true;
        } else if (inEmail) {
            currentEmail.push(line);
        }
    }

    // 保存最后一个邮件
    if (inEmail && currentEmail.length > 0) {
        const emailContent = currentEmail.join('\n').trim();
        if (emailContent) {
            emails.push(emailContent);
        }
    }

    return emails;
}

/**
 * 流式处理大型mbox文件
 */
async function parseStreamMbox(
    filePath: string,
    options: StreamParseOptions = {}
): Promise<ParsedEmail[]> {
    const {
        chunkSize = 1024 * 1024, // 1MB chunks
        maxEmailSize = 50 * 1024 * 1024, // 50MB max per email
        onEmailParsed,
        onProgress
    } = options;

    const results: ParsedEmail[] = [];
    let emailCount = 0;
    let currentEmailBuffer = '';
    let inEmail = false;

    const fileStream = createReadStream(filePath, {
        encoding: 'utf8',
        highWaterMark: chunkSize
    });

    const rl = createInterface({
        input: fileStream,
        crlfDelay: Infinity
    });

    for await (const line of rl) {
        // 检查是否是From行
        if (line.startsWith('From ')) {
            // 处理前一个邮件
            if (inEmail && currentEmailBuffer.trim()) {
                try {
                    const parsedEmail = parseEmail(currentEmailBuffer);
                    results.push(parsedEmail);

                    if (onEmailParsed) {
                        onEmailParsed(parsedEmail, emailCount);
                    }

                    emailCount++;
                    if (onProgress) {
                        onProgress(emailCount);
                    }
                } catch (err) {
                    console.warn(`Failed to parse email ${emailCount}:`, err);
                }
            }

            // 开始新邮件
            currentEmailBuffer = line + '\n';
            inEmail = true;
        } else if (inEmail) {
            currentEmailBuffer += line + '\n';

            // 检查邮件大小限制
            if (currentEmailBuffer.length > maxEmailSize) {
                console.warn(`Email ${emailCount} exceeds size limit, skipping...`);
                currentEmailBuffer = '';
                inEmail = false;
            }
        }
    }

    // 处理最后一个邮件
    if (inEmail && currentEmailBuffer.trim()) {
        try {
            const parsedEmail = parseEmail(currentEmailBuffer);
            results.push(parsedEmail);

            if (onEmailParsed) {
                onEmailParsed(parsedEmail, emailCount);
            }

            emailCount++;
            if (onProgress) {
                onProgress(emailCount);
            }
        } catch (err) {
            console.warn(`Failed to parse final email:`, err);
        }
    }

    return results;
}

// ============= 公共API函数 =============

/**
 * 解析EML格式邮件（单个邮件文件）
 */
function parseEml(emlString: string): ParsedEmail {
    return parseEmail(emlString);
}

/**
 * 解析mbox格式邮件（多个邮件的集合）
 */
function parseMbox(mboxString: string): ParsedEmail[] {
    return splitMboxContent(mboxString).map(parseEmail);
}

/**
 * 自动检测并解析邮件格式（支持mbox和eml）
 */
function parseEmailFile(content: string, filename?: string): ParsedEmail[] {
    if (isEmlFormat(content, filename)) {
        return [parseEml(content)];
    } else {
        return parseMbox(content);
    }
}


// ============= 工具函数 =============

/**
 * 优化的文件处理 - 支持流式处理和批量写入
 */
async function processFile(filePath: string, useStreaming: boolean = true): Promise<void> {
    if (!fs.existsSync(filePath)) {
        console.log(`File not found: ${filePath}, skipping...`);
        return;
    }

    console.log(`\nProcessing file: ${filePath}`);
    const startTime = Date.now();

    let parsedEmails: ParsedEmail[];

    if (useStreaming && filePath.toLowerCase().endsWith('.mbox')) {
        // 使用流式处理大型mbox文件
        console.log('Using streaming mode for large file...');

        parsedEmails = await parseStreamMbox(filePath, {
            chunkSize: 2 * 1024 * 1024, // 2MB chunks
            onProgress: (count) => {
                if (count % 100 === 0) {
                    console.log(`Processed ${count} emails...`);
                }
            }
        });
    } else {
        const content = fs.readFileSync(filePath, "utf-8");
        parsedEmails = parseEmailFile(content, filePath);
    }

    const outputFile = filePath.replace(/\.(eml|mbox)$/, '_parsed.json');

    // 批量写入，避免一次性序列化大量数据
    if (parsedEmails.length > 1000) {
        console.log('Writing large result in batches...');
        await writeLargeJsonFile(outputFile, parsedEmails);
    } else {
        fs.writeFileSync(outputFile, JSON.stringify(parsedEmails, null, 2));
    }

    const endTime = Date.now();
    const duration = (endTime - startTime) / 1000;
    console.log(`Done! Processed ${parsedEmails.length} emails in ${duration.toFixed(2)}s -> ${outputFile}`);
}

/**
 * 批量写入大型JSON文件
 */
async function writeLargeJsonFile(filePath: string, emails: ParsedEmail[]): Promise<void> {
    const writeStream = fs.createWriteStream(filePath, { encoding: 'utf8' });

    writeStream.write('[\n');

    const batchSize = 100;
    for (let i = 0; i < emails.length; i += batchSize) {
        const batch = emails.slice(i, i + batchSize);

        for (let j = 0; j < batch.length; j++) {
            const email = batch[j];
            const isLast = (i + j) === emails.length - 1;

            writeStream.write('  ');
            writeStream.write(JSON.stringify(email, null, 2).replace(/\n/g, '\n  '));

            if (!isLast) {
                writeStream.write(',');
            }
            writeStream.write('\n');
        }

        // 让事件循环有机会处理其他任务
        await new Promise(resolve => setImmediate(resolve));
    }

    writeStream.write(']');
    writeStream.end();

    return new Promise((resolve, reject) => {
        writeStream.on('finish', resolve);
        writeStream.on('error', reject);
    });
}

/**
 * 测试编码修复功能
 */
function testEncodingFixes() {
    console.log('\n=== 测试编码修复功能 ===');

    const testCases = [
        '=C2=A0', // 不间断空格
        'Our new NFT Owners endpoint ( https://docs.moralis.com/web3-data-api/evm/re=\nference/get-nft-owners?utm_campaign=3DNFT+Owners+Endpoint+Product+Email&utm=\n_content=3DNFT+Owners+Endpoint&utm_medium=3Demail_action&utm_source=3Dcusto=\nmer.io ) is perfect for:',
        '=E2=80=99Hello=E2=80=99', // 单引号
        '=F0=9F=98=80=F0=9F=91=8D', // emoji
    ];

    for (const testCase of testCases) {
        console.log(`原始: ${testCase}`);
        console.log(`解码后: ${decodeQuotedPrintable(testCase)}`);
        console.log(`修复后: ${fixHtmlEncoding(decodeQuotedPrintable(testCase))}`);
        console.log('---');
    }
}

/**
 * 主函数 - 支持多种邮件格式和流式处理
 */
const main = async () => {
    try {
        // 先测试编码修复
        testEncodingFixes();

        const testFiles = [
            './111.eml',
            './222.eml',
            './All mail Including Spam and Trash.mbox',
            'sss3.mbox',
            'oldmail.mbox',
        ];

        for (const filePath of testFiles) {
            await processFile(filePath, true); // 启用流式处理
        }
    } catch (error) {
        console.error('Error processing email files:', error);
    }
};

// ============= 导出和执行 =============
export {
    parseEml,
    parseMbox,
    parseEmailFile,
    parseEmail,
    parseStreamMbox,
    processFile,
    type StreamParseOptions
};

main();

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>邮件文件上传</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }

        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }

        .upload-area {
            border: 2px dashed #ddd;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            margin-bottom: 20px;
            transition: border-color 0.3s;
        }

        .upload-area:hover {
            border-color: #007bff;
        }

        .upload-area.dragover {
            border-color: #007bff;
            background-color: #f8f9fa;
        }

        input[type="file"] {
            margin: 20px 0;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            width: 100%;
        }

        button {
            background-color: #007bff;
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            width: 100%;
            margin-top: 10px;
        }

        button:hover {
            background-color: #0056b3;
        }

        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }

        .progress {
            width: 100%;
            height: 20px;
            background-color: #f0f0f0;
            border-radius: 10px;
            margin: 20px 0;
            overflow: hidden;
            display: none;
        }

        .progress-bar {
            height: 100%;
            background-color: #007bff;
            width: 0%;
            transition: width 0.3s;
        }

        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            display: none;
        }

        .result.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .result.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .file-info {
            margin-top: 10px;
            padding: 10px;
            background-color: #f8f9fa;
            border-radius: 5px;
            display: none;
        }

        .preview-container {
            margin-top: 20px;
            display: none;
        }

        .preview-container h3 {
            color: #333;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #007bff;
        }

        .iframe-item {
            margin-bottom: 30px;
            border: 1px solid #ddd;
            border-radius: 5px;
            overflow: hidden;
        }

        .iframe-item h4 {
            margin: 0;
            padding: 15px;
            background-color: #f8f9fa;
            border-bottom: 1px solid #ddd;
            color: #555;
            font-size: 14px;
        }

        .iframe-wrapper {
            padding: 15px;
        }

        iframe {
            width: 100%;
            height: 500px;
            border: 1px solid #ddd;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>邮件文件上传</h1>

        <div class="upload-area" id="uploadArea">
            <p>拖拽文件到此处或点击选择文件</p>
            <input type="file" id="fileInput" accept=".mbox,.eml,.msg">
        </div>

        <div class="file-info" id="fileInfo">
            <strong>选中文件:</strong> <span id="fileName"></span><br>
            <strong>文件大小:</strong> <span id="fileSize"></span>
        </div>

        <button id="uploadBtn" disabled>上传文件</button>

        <div class="progress" id="progress">
            <div class="progress-bar" id="progressBar"></div>
        </div>

        <div class="result" id="result"></div>

        <div class="preview-container" id="previewContainer">
            <h3>邮件内容预览</h3>
            <div id="iframeList"></div>
        </div>
    </div>

    <script>
        const uploadArea = document.getElementById('uploadArea');
        const fileInput = document.getElementById('fileInput');
        const fileInfo = document.getElementById('fileInfo');
        const fileName = document.getElementById('fileName');
        const fileSize = document.getElementById('fileSize');
        const uploadBtn = document.getElementById('uploadBtn');
        const progress = document.getElementById('progress');
        const progressBar = document.getElementById('progressBar');
        const result = document.getElementById('result');
        const previewContainer = document.getElementById('previewContainer');
        const iframeList = document.getElementById('iframeList');

        let selectedFile = null;

        // 点击上传区域选择文件
        uploadArea.addEventListener('click', () => {
            fileInput.click();
        });

        // 拖拽功能
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });

        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });

        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                handleFileSelect(files[0]);
            }
        });

        // 文件选择处理
        fileInput.addEventListener('change', (e) => {
            if (e.target.files.length > 0) {
                handleFileSelect(e.target.files[0]);
            }
        });

        function handleFileSelect(file) {
            selectedFile = file;
            fileName.textContent = file.name;
            fileSize.textContent = formatFileSize(file.size);
            fileInfo.style.display = 'block';
            uploadBtn.disabled = false;
            result.style.display = 'none';
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // 上传功能
        uploadBtn.addEventListener('click', uploadFile);

        async function uploadFile() {
            if (!selectedFile) {
                showResult('请先选择文件', 'error');
                return;
            }

            const formData = new FormData();
            formData.append('upload_mail_file', selectedFile);

            uploadBtn.disabled = true;
            progress.style.display = 'block';
            result.style.display = 'none';

            try {
                const xhr = new XMLHttpRequest();

                // 上传进度
                xhr.upload.addEventListener('progress', (e) => {
                    if (e.lengthComputable) {
                        const percentComplete = (e.loaded / e.total) * 100;
                        progressBar.style.width = percentComplete + '%';
                    }
                });

                // 请求完成
                xhr.addEventListener('load', () => {
                    progress.style.display = 'none';
                    uploadBtn.disabled = false;

                    if (xhr.status === 200) {
                        try {
                            const response = JSON.parse(xhr.responseText);
                            handleUploadSuccess(response);
                        } catch (e) {
                            showResult('文件上传成功，但响应格式异常: ' + xhr.responseText, 'error');
                        }
                    } else {
                        showResult(`上传失败，状态码: ${xhr.status}，响应: ${xhr.responseText}`, 'error');
                        previewContainer.style.display = 'none';
                    }
                });

                // 请求错误
                xhr.addEventListener('error', () => {
                    progress.style.display = 'none';
                    uploadBtn.disabled = false;
                    showResult('网络错误，请检查网络连接', 'error');
                });

                // 发送请求
                xhr.open('POST', 'https://icp.dmail.ai/api/test/v6/upload_mail_file');
                xhr.setRequestHeader('Accept', 'application/json');
                xhr.send(formData);

            } catch (error) {
                progress.style.display = 'none';
                uploadBtn.disabled = false;
                showResult('上传出错: ' + error.message, 'error');
            }
        }

        function handleUploadSuccess(response) {
            // 检查响应结构
            if (response.success && response.code === 1 && response.data && Array.isArray(response.data)) {
                const validItems = response.data.filter(item => item && item.html);

                if (validItems.length > 0) {
                    showResult(`文件上传成功！找到 ${validItems.length} 个邮件`, 'success');
                    renderMailPreviews(validItems);
                } else {
                    showResult('上传成功，但未找到有效的邮件HTML内容', 'error');
                }
            } else {
                showResult('上传成功，但响应格式不符合预期: ' + JSON.stringify(response, null, 2), 'error');
            }
        }

        function renderMailPreviews(mailItems) {
            // 清空之前的内容
            iframeList.innerHTML = '';

            // 为每个邮件项创建iframe
            mailItems.forEach((item, index) => {
                const iframeItem = document.createElement('div');
                iframeItem.className = 'iframe-item';

                const header = document.createElement('h4');
                header.textContent = `邮件 ${index + 1}`;

                const wrapper = document.createElement('div');
                wrapper.className = 'iframe-wrapper';

                const iframe = document.createElement('iframe');
                iframe.srcdoc = item.html;
                iframe.style.width = '100%';
                iframe.style.height = '500px';
                iframe.style.border = '1px solid #ddd';
                iframe.style.borderRadius = '3px';

                wrapper.appendChild(iframe);
                iframeItem.appendChild(header);
                iframeItem.appendChild(wrapper);
                iframeList.appendChild(iframeItem);
            });

            // 显示预览容器
            previewContainer.style.display = 'block';

            // 滚动到预览区域
            setTimeout(() => {
                previewContainer.scrollIntoView({ behavior: 'smooth' });
            }, 100);
        }

        function showResult(message, type) {
            result.textContent = message;
            result.className = `result ${type}`;
            result.style.display = 'block';
        }
    </script>
</body>
</html>

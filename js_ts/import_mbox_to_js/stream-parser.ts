import { Transform, Readable, TransformCallback } from "node:stream";
import { parseEmail, ParsedEmail } from "./parse";

// ============= 流处理相关类型定义 =============
interface EmailParserOptions {
    format?: 'auto' | 'mbox' | 'eml';
    highWaterMark?: number;
    objectMode?: boolean;
    maxBufferSize?: number;
    continueOnError?: boolean;
    errorCallback?: (error: Error, context: string) => void;
    // 新增的错误处理和性能选项
    maxRetries?: number;
    retryDelay?: number;
    enableBackpressure?: boolean;
    memoryPressureThreshold?: number;
    enableGarbageCollection?: boolean;
}

interface StreamParserState {
    buffer: string;
    currentEmail: string;
    emailBoundaryPattern: RegExp;
    isProcessingEmail: boolean;
    bytesProcessed: number;
    emailsEmitted: number;
}

interface EmailBoundary {
    start: number;
    end: number;
    type: 'mbox-from' | 'email-end';
}

interface ErrorHandlingOptions {
    continueOnError: boolean;
    maxBufferSize: number;
    errorCallback?: (error: Error, context: string) => void;
}

// ============= 基础邮件解析流类 =============

/**
 * 基础邮件解析流类
 */
class EmailParserStream extends Transform {
    protected state: StreamParserState;
    protected options: EmailParserOptions;
    protected errorHandling: ErrorHandlingOptions;

    constructor(options: EmailParserOptions = {}) {
        super({
            objectMode: true,
            highWaterMark: options.highWaterMark || 16
        });

        this.options = {
            format: options.format || 'auto',
            maxBufferSize: options.maxBufferSize || 10 * 1024 * 1024, // 10MB default
            continueOnError: options.continueOnError !== false, // default true
            maxRetries: options.maxRetries || 3,
            retryDelay: options.retryDelay || 1000,
            enableBackpressure: options.enableBackpressure !== false, // default true
            memoryPressureThreshold: options.memoryPressureThreshold || 0.7,
            enableGarbageCollection: options.enableGarbageCollection || false,
            ...options
        };

        this.state = {
            buffer: '',
            currentEmail: '',
            emailBoundaryPattern: /^From .+$/gm,
            isProcessingEmail: false,
            bytesProcessed: 0,
            emailsEmitted: 0
        };

        this.errorHandling = {
            continueOnError: this.options.continueOnError!,
            maxBufferSize: this.options.maxBufferSize!,
            errorCallback: this.options.errorCallback
        };
    }

    _transform(chunk: Buffer, encoding: string, callback: TransformCallback): void {
        try {
            // 检查背压 - 如果下游处理慢，暂停处理
            if (this.readableLength > this.readableHighWaterMark * 2) {
                // 延迟处理以允许下游消费
                setImmediate(() => {
                    this._transform(chunk, encoding, callback);
                });
                return;
            }

            const chunkStr = chunk.toString('utf8');
            this.state.buffer += chunkStr;
            this.state.bytesProcessed += chunk.length;

            // 检查缓冲区大小限制
            if (this.state.buffer.length > this.errorHandling.maxBufferSize) {
                const error = new Error(`Buffer size exceeded limit: ${this.errorHandling.maxBufferSize} bytes`);
                if (this.errorHandling.continueOnError) {
                    this.handleError(error, 'buffer-overflow');
                    // 智能缓冲区修剪 - 保留最近的数据和重叠区域
                    this.smartBufferTrim();
                } else {
                    return callback(error);
                }
            }

            // 内存压力检测
            if (this.detectMemoryPressure()) {
                this.handleMemoryPressure();
            }

            this.processBuffer();
            callback();
        } catch (error) {
            this.handleError(error as Error, 'transform');
            if (this.errorHandling.continueOnError) {
                callback();
            } else {
                callback(error as Error);
            }
        }
    }

    _flush(callback: TransformCallback): void {
        try {
            // Process any remaining data in buffer
            if (this.state.buffer.trim()) {
                this.processRemainingBuffer();
            }
            callback();
        } catch (error) {
            this.handleError(error as Error, 'flush');
            callback(this.errorHandling.continueOnError ? null : error as Error);
        }
    }

    protected processBuffer(): void {
        // This method will be overridden by specific stream implementations
        // Base implementation does nothing
    }

    protected processRemainingBuffer(): void {
        // Process any remaining content as a single email (for EML format)
        if (this.state.buffer.trim()) {
            this.emitParsedEmail(this.state.buffer.trim());
        }
    }

    protected emitParsedEmail(emailContent: string): void {
        try {
            // 使用现有的parseEmail函数确保一致性
            const parsedEmail = parseEmail(emailContent);
            
            // 验证解析结果
            if (this.isValidParsedEmail(parsedEmail)) {
                this.push(parsedEmail);
                this.state.emailsEmitted++;
                
                // 发出成功解析事件
                this.emit('emailParsed', {
                    index: this.state.emailsEmitted,
                    subject: parsedEmail.subject,
                    from: parsedEmail.from,
                    size: emailContent.length
                });
            } else {
                throw new Error('Parsed email failed validation');
            }
        } catch (error) {
            this.handleError(error as Error, `email-parsing-${this.state.emailsEmitted}`);
            
            // 如果continueOnError为true，发出一个空的解析结果
            if (this.errorHandling.continueOnError) {
                this.push(this.createEmptyParsedEmail());
                this.state.emailsEmitted++;
            }
        }
    }

    private isValidParsedEmail(email: any): boolean {
        // 验证解析的邮件对象是否有效
        return email && 
               typeof email === 'object' &&
               'subject' in email &&
               'html' in email &&
               'attachment' in email &&
               'from' in email &&
               'to' in email &&
               'datetime' in email;
    }

    private createEmptyParsedEmail(): any {
        // 创建一个空的解析结果，保持与ParsedEmail接口一致
        return {
            subject: null,
            html: null,
            htmlHeaders: null,
            attachment: [],
            from: null,
            to: null,
            datetime: null,
        };
    }

    private handleError(error: Error, context: string): void {
        // 增强错误处理，包含更多上下文信息
        const errorInfo = {
            message: error.message,
            context,
            bytesProcessed: this.state.bytesProcessed,
            emailsEmitted: this.state.emailsEmitted,
            bufferSize: this.state.buffer.length,
            timestamp: new Date().toISOString()
        };

        if (this.errorHandling.errorCallback) {
            this.errorHandling.errorCallback(error, JSON.stringify(errorInfo));
        } else {
            console.warn(`EmailParserStream error:`, errorInfo);
        }

        // 发出错误事件，即使在continueOnError模式下也要通知监听者
        this.emit('parseError', { error, context: errorInfo });

        if (!this.errorHandling.continueOnError) {
            this.emit('error', error);
        }
    }

    private detectMemoryPressure(): boolean {
        // 检测内存压力的启发式方法
        const bufferSizeRatio = this.state.buffer.length / this.errorHandling.maxBufferSize;
        const readableBufferRatio = this.readableLength / (this.readableHighWaterMark || 16);
        
        return bufferSizeRatio > 0.7 || readableBufferRatio > 2;
    }

    private handleMemoryPressure(): void {
        // 内存压力处理策略
        this.emit('memoryPressure', {
            bufferSize: this.state.buffer.length,
            readableLength: this.readableLength,
            bytesProcessed: this.state.bytesProcessed
        });

        // 强制垃圾回收提示（如果可用）
        if (global.gc) {
            global.gc();
        }

        // 减少缓冲区大小
        this.smartBufferTrim();
    }

    private smartBufferTrim(): void {
        // 智能缓冲区修剪 - 保留重要数据
        const currentSize = this.state.buffer.length;
        const targetSize = Math.floor(this.errorHandling.maxBufferSize * 0.5);
        
        if (currentSize > targetSize) {
            // 保留缓冲区的后半部分，这通常包含最新的数据
            const keepSize = Math.max(targetSize, 1024); // 至少保留1KB
            this.state.buffer = this.state.buffer.slice(-keepSize);
            
            this.emit('bufferTrimmed', {
                originalSize: currentSize,
                newSize: this.state.buffer.length,
                trimmedBytes: currentSize - this.state.buffer.length
            });
        }
    }

    // 背压控制方法
    protected checkBackpressure(): boolean {
        return this.readableLength > this.readableHighWaterMark;
    }

    protected waitForDrain(): Promise<void> {
        return new Promise((resolve) => {
            if (!this.checkBackpressure()) {
                resolve();
                return;
            }

            const onDrain = () => {
                this.removeListener('drain', onDrain);
                resolve();
            };

            this.once('drain', onDrain);
        });
    }

    // Getter methods for monitoring
    get bytesProcessed(): number {
        return this.state.bytesProcessed;
    }

    get emailsEmitted(): number {
        return this.state.emailsEmitted;
    }

    get bufferSize(): number {
        return this.state.buffer.length;
    }
}

// ============= Mbox格式分割流类 =============

/**
 * Mbox格式分割流 - 将mbox文件分割为单个邮件
 */
class MboxSplitterStream extends EmailParserStream {
    private emailCount: number = 0;
    private lastBoundaryIndex: number = 0;
    private overlapBuffer: string = '';
    private readonly OVERLAP_SIZE = 1024; // 保留1KB重叠以处理跨chunk的边界

    constructor(options: EmailParserOptions = {}) {
        super({ ...options, format: 'mbox' });
    }

    protected processBuffer(): void {
        // 异步处理邮件，但不阻塞transform
        this.findAndProcessEmails().catch(error => {
            this.handleError(error as Error, 'mbox-processing');
        });
    }

    protected processRemainingBuffer(): void {
        // 处理最后一个邮件（如果有的话）
        if (this.state.buffer.trim()) {
            const lastEmailContent = this.extractEmailFromBuffer(this.lastBoundaryIndex, this.state.buffer.length);
            if (lastEmailContent.trim()) {
                this.emitParsedEmail(lastEmailContent);
            }
        }
    }

    private async findAndProcessEmails(): Promise<void> {
        const boundaries = this.findMboxBoundaries(this.state.buffer);
        
        // 处理完整的邮件（在边界之间的）
        for (let i = 0; i < boundaries.length - 1; i++) {
            // 检查背压，如果需要则等待
            if (this.checkBackpressure()) {
                await this.waitForDrain();
            }

            const emailContent = this.extractEmailFromBuffer(boundaries[i].end, boundaries[i + 1].start);
            if (emailContent.trim()) {
                try {
                    this.emitParsedEmail(emailContent);
                    this.emailCount++;
                } catch (error) {
                    this.handleError(error as Error, `mbox-email-${this.emailCount}`);
                    // 继续处理下一个邮件
                }
            }
        }

        // 更新最后处理的边界位置
        if (boundaries.length > 0) {
            this.lastBoundaryIndex = boundaries[boundaries.length - 1].start;
            
            // 保留缓冲区的最后部分以处理可能跨chunk的邮件
            this.trimBuffer();
        }
    }

    private findMboxBoundaries(data: string): EmailBoundary[] {
        const boundaries: EmailBoundary[] = [];
        const fromLineRegex = /^From .+$/gm;
        let match: RegExpExecArray | null;

        // 重置regex的lastIndex以确保从头开始搜索
        fromLineRegex.lastIndex = 0;

        while ((match = fromLineRegex.exec(data)) !== null) {
            boundaries.push({
                start: match.index,
                end: match.index + match[0].length,
                type: 'mbox-from'
            });
        }

        return boundaries;
    }

    private extractEmailFromBuffer(startIndex: number, endIndex: number): string {
        if (startIndex >= endIndex || startIndex < 0) {
            return '';
        }

        const emailContent = this.state.buffer.slice(startIndex, endIndex);
        
        // 移除开头的"From "行（mbox分隔符）
        const lines = emailContent.split('\n');
        if (lines.length > 0 && lines[0].startsWith('From ')) {
            lines.shift(); // 移除第一行
        }

        return lines.join('\n').trim();
    }

    private trimBuffer(): void {
        // 保留缓冲区的最后部分，以处理可能跨chunk的邮件边界
        if (this.state.buffer.length > this.OVERLAP_SIZE && this.lastBoundaryIndex > 0) {
            const keepFromIndex = Math.max(0, this.lastBoundaryIndex - this.OVERLAP_SIZE);
            const trimmedBuffer = this.state.buffer.slice(keepFromIndex);
            
            // 调整lastBoundaryIndex相对于新缓冲区的位置
            this.lastBoundaryIndex = Math.max(0, this.lastBoundaryIndex - keepFromIndex);
            this.state.buffer = trimmedBuffer;
        }
    }

    // 重写getter以包含mbox特定的统计
    get emailCount(): number {
        return this.emailCount;
    }

    get lastProcessedBoundary(): number {
        return this.lastBoundaryIndex;
    }
}

// ============= EML格式解析流类 =============

/**
 * EML格式解析流 - 处理单个邮件文件
 */
class EmlParserStream extends EmailParserStream {
    private isEmailComplete: boolean = false;

    constructor(options: EmailParserOptions = {}) {
        super({ ...options, format: 'eml' });
    }

    protected processBuffer(): void {
        // EML格式通常是单个完整的邮件，我们等待所有数据到达
        // 或者检查是否有明确的邮件结束标志
        this.checkIfEmailComplete();
    }

    protected processRemainingBuffer(): void {
        // 在flush时处理整个缓冲区作为单个邮件
        if (this.state.buffer.trim() && !this.isEmailComplete) {
            this.emitParsedEmail(this.state.buffer.trim());
            this.isEmailComplete = true;
        }
    }

    private checkIfEmailComplete(): void {
        // 对于EML格式，我们可以检查一些标志来确定邮件是否完整
        // 例如：检查是否有完整的头部和正文分隔符
        const buffer = this.state.buffer;
        
        // 检查是否有头部和正文的分隔符（空行）
        const headerBodySeparator = buffer.indexOf('\r\n\r\n') !== -1 || buffer.indexOf('\n\n') !== -1;
        
        if (headerBodySeparator && !this.isEmailComplete) {
            // 如果我们检测到这可能是一个完整的邮件，并且缓冲区大小稳定
            // （即最近没有新数据），我们可以处理它
            this.tryProcessCompleteEmail();
        }
    }

    private tryProcessCompleteEmail(): void {
        // 这个方法可以在检测到完整邮件时立即处理
        // 但为了简单起见，我们主要在flush时处理
        // 这里可以添加更复杂的逻辑来检测邮件边界
        
        const buffer = this.state.buffer.trim();
        if (buffer && this.looksLikeCompleteEmail(buffer)) {
            this.emitParsedEmail(buffer);
            this.isEmailComplete = true;
            this.state.buffer = ''; // 清空缓冲区
        }
    }

    private looksLikeCompleteEmail(content: string): boolean {
        // 简单的启发式检查来确定内容是否像完整的邮件
        const hasHeaders = /^(From|To|Subject|Date|Message-ID):/im.test(content);
        const hasHeaderBodySeparator = content.includes('\r\n\r\n') || content.includes('\n\n');
        
        return hasHeaders && hasHeaderBodySeparator;
    }

    // EML特定的getter
    get isComplete(): boolean {
        return this.isEmailComplete;
    }
}

// ============= 流式API函数 =============

import fs from "node:fs";
import path from "node:path";

/**
 * 从文件路径创建邮件解析流
 */
function parseEmailFileStream(filePath: string, options: EmailParserOptions = {}): EmailParserStream {
    const fileStream = fs.createReadStream(filePath);
    return parseEmailStream(fileStream, filePath, options);
}

/**
 * 从输入流创建邮件解析流
 */
function parseEmailStream(inputStream: Readable, filePathOrName?: string, options: EmailParserOptions = {}): EmailParserStream {
    const format = options.format || detectFormatFromFilename(filePathOrName);
    const parser = createEmailParser(format, options);
    
    // 连接输入流到解析器
    inputStream.pipe(parser);
    
    return parser;
}

/**
 * 创建Mbox格式解析流
 */
function parseMboxStream(inputStream: Readable, options: EmailParserOptions = {}): MboxSplitterStream {
    const parser = new MboxSplitterStream(options);
    inputStream.pipe(parser);
    return parser;
}

/**
 * 创建EML格式解析流
 */
function parseEmlStream(inputStream: Readable, options: EmailParserOptions = {}): EmlParserStream {
    const parser = new EmlParserStream(options);
    inputStream.pipe(parser);
    return parser;
}

/**
 * 创建邮件解析器（工厂函数）
 */
function createEmailParser(format: 'mbox' | 'eml' | 'auto', options: EmailParserOptions = {}): EmailParserStream {
    switch (format) {
        case 'mbox':
            return new MboxSplitterStream(options);
        case 'eml':
            return new EmlParserStream(options);
        case 'auto':
        default:
            // 默认使用mbox格式，因为它可以处理单个邮件
            return new MboxSplitterStream(options);
    }
}

/**
 * 从文件名检测邮件格式
 */
function detectFormatFromFilename(filePathOrName?: string): 'mbox' | 'eml' | 'auto' {
    if (!filePathOrName) return 'auto';
    
    const ext = path.extname(filePathOrName).toLowerCase();
    
    switch (ext) {
        case '.eml':
            return 'eml';
        case '.mbox':
            return 'mbox';
        default:
            // 检查文件名中是否包含格式指示
            const filename = path.basename(filePathOrName).toLowerCase();
            if (filename.includes('mbox') || filename.includes('mailbox')) {
                return 'mbox';
            }
            if (filename.includes('eml') || filename.includes('email')) {
                return 'eml';
            }
            return 'auto';
    }
}

/**
 * 从流的初始数据检测格式（异步）
 */
async function detectFormatFromStream(stream: Readable): Promise<'mbox' | 'eml'> {
    return new Promise((resolve, reject) => {
        let buffer = '';
        let resolved = false;

        const onData = (chunk: Buffer) => {
            if (resolved) return;
            
            buffer += chunk.toString('utf8');
            
            // 检查前1KB数据来确定格式
            if (buffer.length >= 1024) {
                cleanup();
                resolve(analyzeContent(buffer));
                resolved = true;
            }
        };

        const onEnd = () => {
            if (!resolved) {
                cleanup();
                resolve(analyzeContent(buffer));
                resolved = true;
            }
        };

        const onError = (error: Error) => {
            cleanup();
            reject(error);
        };

        const cleanup = () => {
            stream.removeListener('data', onData);
            stream.removeListener('end', onEnd);
            stream.removeListener('error', onError);
        };

        stream.on('data', onData);
        stream.on('end', onEnd);
        stream.on('error', onError);
    });
}

/**
 * 分析内容来确定格式
 */
function analyzeContent(content: string): 'mbox' | 'eml' {
    // 检查是否有mbox格式的"From "行
    const hasMboxFromLine = /^From .+$/m.test(content);
    
    // 检查是否有多个"From "行（表明是mbox格式）
    const fromLineMatches = content.match(/^From .+$/gm);
    const hasMultipleFromLines = fromLineMatches && fromLineMatches.length > 1;
    
    // 检查是否有标准邮件头部
    const hasEmailHeaders = /^(Message-ID|Date|From|To|Subject):/im.test(content);
    
    if (hasMultipleFromLines) {
        return 'mbox';
    } else if (hasMboxFromLine && !hasEmailHeaders) {
        return 'mbox';
    } else {
        return 'eml';
    }
}

// ============= 兼容性适配器函数 =============

/**
 * 将流式解析结果收集为数组（兼容现有API）
 */
function collectStreamResults(parser: EmailParserStream): Promise<any[]> {
    return new Promise((resolve, reject) => {
        const results: any[] = [];
        
        parser.on('data', (parsedEmail) => {
            results.push(parsedEmail);
        });
        
        parser.on('end', () => {
            resolve(results);
        });
        
        parser.on('error', (error) => {
            reject(error);
        });
    });
}

/**
 * 流式版本的parseEmailFile，返回Promise<ParsedEmail[]>以保持兼容性
 */
async function parseEmailFileStreamCompat(filePath: string, options: EmailParserOptions = {}): Promise<any[]> {
    const parser = parseEmailFileStream(filePath, options);
    return collectStreamResults(parser);
}

/**
 * 创建与现有API兼容的流式解析器
 */
function createCompatibleParser(content: string, filename?: string): any[] {
    // 这个函数提供了一个桥梁，允许现有代码逐步迁移到流式处理
    // 注意：这仍然会将内容加载到内存中，但使用了流式解析逻辑
    
    const format = detectFormatFromFilename(filename) || analyzeContent(content);
    const results: any[] = [];
    
    if (format === 'mbox') {
        // 使用现有的splitMboxContent逻辑，但通过流解析器处理每个邮件
        const { splitMboxContent } = require('./parse');
        const emails = splitMboxContent(content);
        
        emails.forEach(emailContent => {
            try {
                const { parseEmail } = require('./parse');
                const parsed = parseEmail(emailContent);
                results.push(parsed);
            } catch (error) {
                console.warn('Failed to parse email in compat mode:', error);
                results.push({
                    subject: null,
                    html: null,
                    htmlHeaders: null,
                    attachment: [],
                    from: null,
                    to: null,
                    datetime: null,
                });
            }
        });
    } else {
        // EML格式
        try {
            const { parseEmail } = require('./parse');
            const parsed = parseEmail(content);
            results.push(parsed);
        } catch (error) {
            console.warn('Failed to parse EML in compat mode:', error);
            results.push({
                subject: null,
                html: null,
                htmlHeaders: null,
                attachment: [],
                from: null,
                to: null,
                datetime: null,
            });
        }
    }
    
    return results;
}

export {
    EmailParserStream,
    MboxSplitterStream,
    EmlParserStream,
    EmailParserOptions,
    StreamParserState,
    EmailBoundary,
    ErrorHandlingOptions,
    // 流式API函数
    parseEmailFileStream,
    parseEmailStream,
    parseMboxStream,
    parseEmlStream,
    createEmailParser,
    detectFormatFromStream,
    detectFormatFromFilename
};
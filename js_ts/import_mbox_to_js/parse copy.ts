import fs from "node:fs";

// ============= 类型定义 =============
interface ParsedEmail {
    subject: string | null;
    html: string | null;
    htmlHeaders: Record<string, string> | null;
    attachment: any[];
    from: string | null;
    to: string | null;
    datetime: Date | null;
}

interface EmailPart {
    headers: Record<string, string>;
    body: string;
    isMultipart: boolean;
    boundary: string | null;
    contentType: string;
    contentDisposition: string | null;
}

// ============= 核心解析函数 =============

/**
 * 解析邮件头部
 */
function parseHeaders(headerString: string): Record<string, string> {
    const headers: Record<string, string> = {};
    const unfoldedHeaders = headerString.replace(/\r?\n[\t ]+/g, ' ');
    const headerLines = unfoldedHeaders.split(/\r?\n/);

    for (const line of headerLines) {
        const colonIndex = line.indexOf(':');
        if (colonIndex > 0) {
            const key = line.slice(0, colonIndex).toLowerCase().trim();
            const value = line.slice(colonIndex + 1).trim();
            headers[key] = value;
        }
    }
    return headers;
}

/**
 * 提取multipart边界
 */
function extractBoundary(contentType: string): string | null {
    const boundaryMatch = contentType.match(/boundary=(?:"([^"]+)"|([^;\s]+))/i);
    return boundaryMatch ? (boundaryMatch[1] || boundaryMatch[2]) : null;
}

/**
 * 解析单个邮件部分
 */
function parseEmailPart(emailPartString: string): EmailPart {
    const headerBodyMatch = emailPartString.match(/^([\s\S]*?)\r?\n\r?\n([\s\S]*)$/);
    if (!headerBodyMatch) {
        return {
            headers: {},
            body: emailPartString,
            isMultipart: false,
            boundary: null,
            contentType: 'text/plain',
            contentDisposition: null,
        };
    }

    const [, headerString, body] = headerBodyMatch;
    const headers = parseHeaders(headerString);
    const contentType = headers['content-type'] || 'text/plain';
    const isMultipart = contentType.startsWith('multipart/');
    const boundary = isMultipart ? extractBoundary(contentType) : null;

    return {
        headers,
        body,
        isMultipart,
        boundary,
        contentType,
        contentDisposition: headers['content-disposition'],
    };
}

// ============= 内容解码函数 =============

/**
 * 解码quoted-printable内容
 */
function decodeQuotedPrintable(content: string): string {
    return content
        .replace(/=\r?\n/g, '')  // 移除软换行
        .replace(/=([0-9A-F]{2})/gi, (_: string, hex: string) => {
            return String.fromCharCode(parseInt(hex, 16));
        });
}

/**
 * 解码base64内容
 */
function decodeBase64(content: string): string {
    try {
        const cleanBase64 = content.replace(/\s/g, '');
        return Buffer.from(cleanBase64, 'base64').toString('utf8');
    } catch (e) {
        console.warn('Failed to decode base64 content:', e);
        return content;
    }
}

/**
 * 根据编码类型解码内容
 */
function decodeContent(content: string, encoding: string): string {
    if (!content) return '';

    switch (encoding?.toLowerCase()) {
        case 'quoted-printable':
            return decodeQuotedPrintable(content);
        case 'base64':
            return decodeBase64(content);
        case '7bit':
        case '8bit':
        case 'binary':
        default:
            return content;
    }
}



// ============= 内容处理函数 =============

/**
 * 处理HTML内容
 */
function processHtmlPart(part: EmailPart): { content: string; headers: Record<string, string> } {
    const encoding = part.headers['content-transfer-encoding'];
    const decodedHtml = decodeContent(part.body, encoding).trim();

    return {
        content: decodedHtml,
        headers: {
            'Content-Type': 'text/html; charset=utf-8',
            'Content-Transfer-Encoding': 'decoded'
        }
    };
}

/**
 * 提取附件文件名
 */
function extractFilename(contentDisposition: string): string {
    const filenameMatch = contentDisposition.match(/filename\*?=(?:"([^"]+)"|([^;\s]+))/i);
    if (!filenameMatch) return 'unnamed';

    let filename = filenameMatch[1] || filenameMatch[2];
    // 处理RFC 2231编码的文件名
    if (filename.includes("''")) {
        filename = decodeURIComponent(filename.split("''")[1]);
    }
    return filename;
}

/**
 * 处理附件内容
 */
function processAttachmentPart(part: EmailPart): any {
    const filename = extractFilename(part.contentDisposition || '');
    const originalContentType = part.contentType.split(';')[0].trim();
    const encoding = part.headers['content-transfer-encoding']?.toLowerCase();

    let content = part.body;
    if (encoding === 'base64') {
        content = content.replace(/\s/g, '');
    } else {
        const decodedContent = decodeContent(content, encoding || '');
        content = Buffer.from(decodedContent, 'binary').toString('base64');
    }

    return {
        "content-Type": originalContentType,
        "filename": filename,
        "base64": content
    };
}

/**
 * 分割multipart内容
 */
function splitMultipartContent(body: string, boundary: string): string[] {
    const parts = body.split(new RegExp(`^--${boundary.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}(?:--)?\\s*$`, 'gm'));
    return parts.slice(1).map(p => p.trim()).filter(p => p && p !== '--');
}

/**
 * 处理邮件正文内容
 */
function processBody(part: EmailPart): { html: { content: string; headers: Record<string, string> } | null; attachments: any[] } {
    let html: { content: string; headers: Record<string, string> } | null = null;
    let attachments: any[] = [];

    try {
        if (!part?.body) return { html, attachments };

        if (part.isMultipart && part.boundary) {
            // 处理multipart内容
            const parts = splitMultipartContent(part.body, part.boundary);

            for (const partContent of parts) {
                try {
                    const subPart = parseEmailPart(partContent);
                    const result = processBody(subPart);

                    if (result.html) html = result.html;
                    if (result.attachments.length) attachments.push(...result.attachments);
                } catch (err) {
                    console.warn('Failed to process email part:', err);
                }
            }
        } else if (part.contentType.includes('text/html') &&
            (!part.contentDisposition || !part.contentDisposition.includes('attachment'))) {
            // 处理HTML内容
            html = processHtmlPart(part);
        } else if (part.contentDisposition?.includes('attachment')) {
            // 处理附件
            attachments.push(processAttachmentPart(part));
        }
    } catch (err) {
        console.warn('Error in processBody:', err);
    }

    return { html, attachments };
}


// ============= 邮件字段处理函数 =============

/**
 * 解码邮件主题（RFC 2047）
 */
function decodeEmailSubject(subject: string): string {
    if (!subject) return subject;

    return subject.replace(/=\?([^?]+)\?([BQ])\?(.*?)\?=/gi, (match, _charset, encoding, encodedText) => {
        try {
            if (encoding.toUpperCase() === 'B') {
                return Buffer.from(encodedText, 'base64').toString('utf8');
            } else if (encoding.toUpperCase() === 'Q') {
                return encodedText
                    .replace(/_/g, ' ')
                    .replace(/=([0-9A-F]{2})/gi, (_: string, hex: string) => {
                        return String.fromCharCode(parseInt(hex, 16));
                    });
            }
            return match;
        } catch (e) {
            console.warn('Failed to decode subject:', e);
            return match;
        }
    });
}

/**
 * 提取邮件地址
 */
function extractEmail(fromField: string): string | null {
    if (!fromField) return null;

    // 处理 "Name <<EMAIL>>" 格式
    const angleMatch = fromField.match(/<([^>]+)>/);
    if (angleMatch) return angleMatch[1].trim();

    // 直接匹配邮件地址
    const emailMatch = fromField.match(/\b[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}\b/);
    return emailMatch ? emailMatch[0] : null;
}

/**
 * 安全解析日期
 */
function parseEmailDate(dateString: string): Date | null {
    if (!dateString) return null;

    try {
        const date = new Date(dateString);
        return isNaN(date.getTime()) ? null : date;
    } catch (e) {
        console.warn('Invalid date format:', dateString);
        return null;
    }
}

// ============= 主要解析函数 =============

/**
 * 解析单个邮件内容为标准格式
 */
function parseEmail(emailString: string): ParsedEmail {
    try {
        const mainPart = parseEmailPart(emailString);
        const { html, attachments } = processBody(mainPart);

        return {
            subject: mainPart.headers.subject ? decodeEmailSubject(mainPart.headers.subject) : null,
            html: html?.content || null,
            htmlHeaders: html?.headers || null,
            attachment: attachments,
            from: extractEmail(mainPart.headers.from || ''),
            to: extractEmail(mainPart.headers.to || ''),
            datetime: parseEmailDate(mainPart.headers.date || ''),
        };
    } catch (err) {
        console.warn('Failed to parse email:', err);
        return {
            subject: null,
            html: null,
            htmlHeaders: null,
            attachment: [],
            from: null,
            to: null,
            datetime: null,
        };
    }
}

// ============= 格式检测和分割函数 =============

/**
 * 检测是否为EML格式
 */
function isEmlFormat(content: string, filename?: string): boolean {
    return filename?.toLowerCase().endsWith('.eml') ||
        (!content.includes('\nFrom ') &&
            (content.includes('Message-ID:') || content.includes('Date:') || content.includes('Subject:')));
}

/**
 * 分割mbox内容为单个邮件
 */
function splitMboxContent(mboxString: string): string[] {
    const fromLineRegex = /^From .+$/gm;
    const emails: string[] = [];
    let lastIndex = 0;
    let match: RegExpExecArray | null;

    while ((match = fromLineRegex.exec(mboxString)) !== null) {
        if (lastIndex > 0) {
            emails.push(mboxString.slice(lastIndex, match.index).trim());
        }
        lastIndex = match.index;
    }

    if (lastIndex > 0) {
        emails.push(mboxString.slice(lastIndex).trim());
    }

    return emails.filter(email => email.trim() !== '');
}

// ============= 公共API函数 =============

/**
 * 解析EML格式邮件（单个邮件文件）
 */
function parseEml(emlString: string): ParsedEmail {
    return parseEmail(emlString);
}

/**
 * 解析mbox格式邮件（多个邮件的集合）
 */
function parseMbox(mboxString: string): ParsedEmail[] {
    return splitMboxContent(mboxString).map(parseEmail);
}

/**
 * 自动检测并解析邮件格式（支持mbox和eml）
 */
function parseEmailFile(content: string, filename?: string): ParsedEmail[] {
    if (isEmlFormat(content, filename)) {
        return [parseEml(content)];
    } else {
        return parseMbox(content);
    }
}


// ============= 工具函数 =============

/**
 * 处理单个文件
 */
function processFile(filePath: string): void {
    if (!fs.existsSync(filePath)) {
        console.log(`File not found: ${filePath}, skipping...`);
        return;
    }

    console.log(`\nProcessing file: ${filePath}`);
    const content = fs.readFileSync(filePath, "utf-8");
    const parsedEmails = parseEmailFile(content, filePath);

    const outputFile = filePath.replace(/\.(eml|mbox)$/, '_parsed.json');
    fs.writeFileSync(outputFile, JSON.stringify(parsedEmails, null, 2));

    console.log(`Done! Processed ${parsedEmails.length} emails -> ${outputFile}`);
}

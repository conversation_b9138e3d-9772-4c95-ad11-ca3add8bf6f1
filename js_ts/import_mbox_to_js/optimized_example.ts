import { 
    parseStreamMbox, 
    processFile, 
    parseEmailFile,
    type StreamParseOptions 
} from './parse.js';
import fs from 'node:fs';

/**
 * 优化后的邮件解析器使用示例
 */

// ============= 示例1: 流式处理大型mbox文件 =============
async function streamProcessLargeMbox() {
    console.log('=== 流式处理大型mbox文件 ===');
    
    const filePath = './All mail Including Spam and Trash.mbox';
    
    if (!fs.existsSync(filePath)) {
        console.log('大型mbox文件不存在，跳过示例');
        return;
    }
    
    const options: StreamParseOptions = {
        chunkSize: 2 * 1024 * 1024, // 2MB 块大小
        maxEmailSize: 50 * 1024 * 1024, // 50MB 最大邮件大小
        onEmailParsed: (email, index) => {
            // 实时处理每个解析的邮件
            console.log(`解析邮件 ${index + 1}: ${email.subject || '无主题'}`);
            
            // 可以在这里进行实时处理，比如：
            // - 保存到数据库
            // - 过滤特定邮件
            // - 提取附件
        },
        onProgress: (processed) => {
            if (processed % 50 === 0) {
                console.log(`已处理 ${processed} 封邮件...`);
            }
        }
    };
    
    try {
        const startTime = Date.now();
        const emails = await parseStreamMbox(filePath, options);
        const endTime = Date.now();
        
        console.log(`流式处理完成！`);
        console.log(`总邮件数: ${emails.length}`);
        console.log(`处理时间: ${(endTime - startTime) / 1000}秒`);
        console.log(`平均速度: ${(emails.length / ((endTime - startTime) / 1000)).toFixed(2)} 邮件/秒`);
        
        // 统计信息
        const withAttachments = emails.filter(e => e.attachment.length > 0).length;
        const withHtml = emails.filter(e => e.html).length;
        
        console.log(`包含附件的邮件: ${withAttachments}`);
        console.log(`包含HTML的邮件: ${withHtml}`);
        
    } catch (error) {
        console.error('流式处理失败:', error);
    }
}

// ============= 示例2: 批量处理多个文件 =============
async function batchProcessFiles() {
    console.log('\n=== 批量处理多个文件 ===');
    
    const files = [
        './222.eml',
        './All mail Including Spam and Trash.mbox'
    ];
    
    for (const filePath of files) {
        if (fs.existsSync(filePath)) {
            console.log(`\n处理文件: ${filePath}`);
            
            // 根据文件大小选择处理方式
            const stats = fs.statSync(filePath);
            const fileSizeMB = stats.size / (1024 * 1024);
            
            console.log(`文件大小: ${fileSizeMB.toFixed(2)} MB`);
            
            // 大于10MB的文件使用流式处理
            const useStreaming = fileSizeMB > 10;
            
            await processFile(filePath, useStreaming);
        } else {
            console.log(`文件不存在: ${filePath}`);
        }
    }
}

// ============= 示例3: 内存使用监控 =============
function monitorMemoryUsage() {
    const used = process.memoryUsage();
    console.log('\n=== 内存使用情况 ===');
    for (let key in used) {
        console.log(`${key}: ${Math.round(used[key as keyof typeof used] / 1024 / 1024 * 100) / 100} MB`);
    }
}

// ============= 示例4: 性能对比 =============
async function performanceComparison() {
    console.log('\n=== 性能对比测试 ===');
    
    const testFile = './All mail Including Spam and Trash.mbox';
    
    if (!fs.existsSync(testFile)) {
        console.log('测试文件不存在，跳过性能对比');
        return;
    }
    
    const stats = fs.statSync(testFile);
    const fileSizeMB = stats.size / (1024 * 1024);
    
    if (fileSizeMB > 100) {
        console.log(`文件太大 (${fileSizeMB.toFixed(2)} MB)，跳过传统方式测试`);
        
        // 只测试流式处理
        console.log('测试流式处理...');
        monitorMemoryUsage();
        
        const startTime = Date.now();
        const emails = await parseStreamMbox(testFile, {
            onProgress: (count) => {
                if (count % 100 === 0) {
                    monitorMemoryUsage();
                }
            }
        });
        const endTime = Date.now();
        
        console.log(`流式处理结果:`);
        console.log(`- 邮件数量: ${emails.length}`);
        console.log(`- 处理时间: ${(endTime - startTime) / 1000}秒`);
        monitorMemoryUsage();
        
    } else {
        console.log('文件大小适中，进行完整性能对比...');
        
        // 测试传统方式
        console.log('\n1. 测试传统方式...');
        monitorMemoryUsage();
        
        const traditionalStart = Date.now();
        const content = fs.readFileSync(testFile, 'utf-8');
        const traditionalEmails = parseEmailFile(content, testFile);
        const traditionalEnd = Date.now();
        
        console.log(`传统方式结果:`);
        console.log(`- 邮件数量: ${traditionalEmails.length}`);
        console.log(`- 处理时间: ${(traditionalEnd - traditionalStart) / 1000}秒`);
        monitorMemoryUsage();
        
        // 测试流式处理
        console.log('\n2. 测试流式处理...');
        
        // 强制垃圾回收（如果可用）
        if (global.gc) {
            global.gc();
        }
        
        monitorMemoryUsage();
        
        const streamStart = Date.now();
        const streamEmails = await parseStreamMbox(testFile);
        const streamEnd = Date.now();
        
        console.log(`流式处理结果:`);
        console.log(`- 邮件数量: ${streamEmails.length}`);
        console.log(`- 处理时间: ${(streamEnd - streamStart) / 1000}秒`);
        monitorMemoryUsage();
        
        // 性能对比
        console.log('\n=== 性能对比总结 ===');
        const traditionalTime = (traditionalEnd - traditionalStart) / 1000;
        const streamTime = (streamEnd - streamStart) / 1000;
        
        console.log(`传统方式: ${traditionalTime.toFixed(2)}秒`);
        console.log(`流式处理: ${streamTime.toFixed(2)}秒`);
        
        if (streamTime < traditionalTime) {
            console.log(`流式处理快 ${((traditionalTime - streamTime) / traditionalTime * 100).toFixed(1)}%`);
        } else {
            console.log(`传统方式快 ${((streamTime - traditionalTime) / streamTime * 100).toFixed(1)}%`);
        }
    }
}

// ============= 主函数 =============
async function main() {
    console.log('邮件解析器性能优化示例\n');
    
    try {
        // 示例1: 流式处理
        await streamProcessLargeMbox();
        
        // 示例2: 批量处理
        await batchProcessFiles();
        
        // 示例3: 性能对比
        await performanceComparison();
        
    } catch (error) {
        console.error('示例执行失败:', error);
    }
}

// 运行示例
if (import.meta.url === `file://${process.argv[1]}`) {
    main();
}

export { 
    streamProcessLargeMbox, 
    batchProcessFiles, 
    performanceComparison,
    monitorMemoryUsage 
};

import {ethers} from "hardhat";
import {DmailToken} from "../typechain-types";
import {solidityPacked} from "ethers";

async function main() {

  // const bscEndPoint = '******************************************'
  // const lzShitcoinGoerli = await ethers.deployContract("DmailToken", [bscEndPoint, 8,"******************************************"]);

  const signer = (await ethers.getSigners())[0]
  console.log("🚀 ~ main ~ signer:", signer.address)
  const ethEndPoint = "******************************************"
  const lzShitcoinGoerli = await ethers.deployContract("DmailToken", [ethEndPoint, 8, signer.address]);



  const dmailToken = await lzShitcoinGoerli.waitForDeployment();
  const caddress = lzShitcoinGoerli.target.toString()
  console.log(
    `lzShitcoin deployed to ${caddress}`
  )
  const setTrustedRemoteAddress_hash = await dmailToken.setTrustedRemoteAddress(183, caddress)//.then(async (v) => await v.wait())
  if (setTrustedRemoteAddress_hash) {
    console.log("🚀 ~ main ~ setTrustedRemoteAddress_hash:", setTrustedRemoteAddress_hash.hash)
  }

  await dmailToken.setMinDstGas(183, 0, 200000)
  await dmailToken.setMinDstGas(183, 1, 200000)
  // await remoteOFT.setMinDstGas(localChainId, 0, 200000)
  // await remoteOFT.setMinDstGas(localChainId, 1, 200000)
  const remotePath = solidityPacked(["address", "address"], [caddress, caddress])
  const a = await dmailToken.setTrustedRemote(183, remotePath)//.then(async a => await a.wait())
  if (a) console.log('a.hash', a.hash)


  //npx hardhat verify --contract contracts/DmailToken.sol:DmailToken  --network linea ****************************************** "******************************************" 8




  // const lzShitcoinGoerli = await ethers.deployContract("LayerZeroShitcoin", [name, symbol, lzEndpointGoerli]);
  // await lzShitcoinGoerli.waitForDeployment();
  // console.log(
  //   `lzShitcoin deployed to ${lzShitcoinGoerli.target}`
  // );

  // const lzShitcoinFantomTestnet = await ethers.deployContract("LayerZeroShitcoin", [name, symbol, lzEndPointFantomTestnet]);
  // await lzShitcoinFantomTestnet.waitForDeployment();
  // console.log(
  //   `lzShitcoin deployed to ${lzShitcoinFantomTestnet.target}`
  // );
}

// We recommend this pattern to be able to use async/await everywhere
// and properly handle errors.
main().catch(console.error).finally(() => process.exit());

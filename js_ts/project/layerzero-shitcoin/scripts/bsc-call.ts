

import {ethers, solidityPacked} from "ethers";
import {abia} from "./abi.js"


const start = async () => {


    const privateKey = "5f871e25e649958b2539e23dda10c43e73abfe9281f36120fd7da30dcab36c0d"; // 替换为你的私钥
    const caddress = "******************************************"
    const provider = new ethers.JsonRpcProvider('https://bsc-dataseed2.defibit.io')
    const wallet = new ethers.Wallet(privateKey, provider);
    console.log('wallet', wallet.address)
    const dmailToken = new ethers.Contract(caddress, abia, wallet)

    // ==============
    const setTrustedRemoteAddress_hash = await dmailToken.setTrustedRemoteAddress(183, caddress)//.then(async (v) => await v.wait())
    if (setTrustedRemoteAddress_hash) {
        console.log("🚀 ~ main ~ setTrustedRemoteAddress_hash:", setTrustedRemoteAddress_hash.hash)
    }

    await dmailToken.setMinDstGas(183, 0, 200000)
    await dmailToken.setMinDstGas(183, 1, 200000)
    // await remoteOFT.setMinDstGas(localChainId, 0, 200000)
    // await remoteOFT.setMinDstGas(localChainId, 1, 200000)
    const remotePath = solidityPacked(["address", "address"], [caddress, caddress])
    const a = await dmailToken.setTrustedRemote(183, remotePath)//.then(async a => await a.wait())
    if (a) console.log('a.hash', a.hash)


}

const main = (async () => {
    try {
        // require("../biz_util/init_biz_middleware.js")
        // const {init_server_middleware} = await import("../server_util/init_server_middleware.js")
        // await init_server_middleware()
        start()
    } catch (err) {
        console.log('err', err)
    }
})()
import {ethers} from "hardhat";
import {DToken} from "../typechain-types/contracts/Dmail20.sol";
import {DmailToken} from "../typechain-types";

async function main() {

  // const bscEndPoint = '******************************************'
  // const lzShitcoinGoerli = await ethers.deployContract("DToken", [bscEndPoint, 8]);


  // const ethEndPoint = "******************************************"
  // const lzShitcoinGoerli = await ethers.deployContract("DToken", [ethEndPoint, 8]);

  const signer = (await ethers.getSigners())[0]
  console.log('signer', signer.address)

  const contractFactory = await ethers.getContractFactory("DmailToken", signer)
  const lzapp = await contractFactory.attach("******************************************") as DmailToken
  const amount = await lzapp.balanceOf(signer.address)
  console.log('amount', amount)
  // await lzapp.setMinDstGas(183, 0, 200000).then(async v=> v.wait())
  // await lzapp.setMinDstGas(183, 1, 200000).then(async v => v.wait())

  const bobAddressBytes32 = ethers.AbiCoder.defaultAbiCoder().encode(["address"], ["******************************************"])
  console.log('bobAddressBytes32', bobAddressBytes32)
  let defaultAdapterParams = ethers.solidityPacked(["uint16", "uint256"], [1, 200000])
  console.log('defaultAdapterParams', defaultAdapterParams)
  const initialAmount = ethers.parseEther("1.50000000")
  let nativeFee = (await lzapp.estimateSendFee(102, bobAddressBytes32, initialAmount, false, defaultAdapterParams)).nativeFee
  console.log('nativeFee', nativeFee)



  // @ts-ignore
  const a = await lzapp.connect(signer).sendFrom(signer.address.toString(),
    102,
    bobAddressBytes32,
    initialAmount,
    [signer.address, ethers.ZeroAddress, defaultAdapterParams],
    {value: nativeFee}
  )
  console.log('a', a)





















  // const lzShitcoinGoerli = await ethers.deployContract("LayerZeroShitcoin", [name, symbol, lzEndpointGoerli]);
  // await lzShitcoinGoerli.waitForDeployment();
  // console.log(
  //   `lzShitcoin deployed to ${lzShitcoinGoerli.target}`
  // );

  // const lzShitcoinFantomTestnet = await ethers.deployContract("LayerZeroShitcoin", [name, symbol, lzEndPointFantomTestnet]);
  // await lzShitcoinFantomTestnet.waitForDeployment();
  // console.log(
  //   `lzShitcoin deployed to ${lzShitcoinFantomTestnet.target}`
  // );
}

// We recommend this pattern to be able to use async/await everywhere
// and properly handle errors.
main().catch((error) => {
  console.error(error);
  process.exitCode = 1;
});

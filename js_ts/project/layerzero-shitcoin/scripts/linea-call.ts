import {ethers, solidityPacked} from "ethers";
import {abia} from "./abi.js"


const start = async () => {


    const privateKey = "5f871e25e649958b2539e23dda10c43e73abfe9281f36120fd7da30dcab36c0d"; // 替换为你的私钥
    const caddress = "******************************************"
    const provider = new ethers.JsonRpcProvider('https://linea.blockpi.network/v1/rpc/public')
    const wallet = new ethers.Wallet(privateKey, provider);
    console.log('wallet',wallet.address)
    const dmailToken = new ethers.Contract(caddress, abia, wallet)
    // const remotePath = solidityPacked(["address", "address"], [caddress, caddress])
    // const a = await dmailToken.setTrustedRemote(102, remotePath).then(async af => await af.wait())

    // const setTrustedRemoteAddress_hash_eth = await dmailToken.setTrustedRemoteAddress(101, caddress).then(async v => await v.wait())
    // if (setTrustedRemoteAddress_hash_eth) console.log("🚀 ~ main ~ setTrustedRemoteAddress_hash_eth:", setTrustedRemoteAddress_hash_eth.hash)
    // await dmailToken.setMinDstGas(101, 0, 200000)
    await dmailToken.setMinDstGas(101, 1, 200000)
    const remotePath_eth = solidityPacked(["address", "address"], [caddress, caddress])
    const b = await dmailToken.setTrustedRemote(101, remotePath_eth).then(async af => await af.wait())
    if (b) console.log('b.hash', b.hash)


}

const main = (async () => {
    try {
        // require("../biz_util/init_biz_middleware.js")
        // const {init_server_middleware} = await import("../server_util/init_server_middleware.js")
        // await init_server_middleware()
        start()
    } catch (err) {
        console.log('err', err)
    }
})()
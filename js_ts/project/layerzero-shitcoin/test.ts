import {solidityPacked,AbiCoder} from "ethers"
const localPath = solidityPacked(["address", "address"], ["******************************************", "******************************************"])
console.log('localPath', localPath)

// const bobAddressBytes32 = AbiCoder.defaultAbiCoder().encode(["address"], ["******************************************"])
// console.log(`bobAddressBytes32`, bobAddressBytes32)




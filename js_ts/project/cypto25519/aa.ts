const {createECDH} = require('node:crypto');

function method2() {
    try {
        // 使用大写 'X25519' 作为曲线名称
        const alice = createECDH('X25519');
        alice.generateKeys();

        const bob = createECDH('X25519');
        bob.generateKeys();

        const aliceShared = alice.computeSecret(bob.getPublicKey());
        const bobShared = bob.computeSecret(alice.getPublicKey());

        console.log('匹配:', aliceShared.equals(bobShared));
    } catch (error) {
        console.error('错误:', error.message);
    }
}

method2();
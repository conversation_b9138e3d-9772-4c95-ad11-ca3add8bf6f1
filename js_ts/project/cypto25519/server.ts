// server.js
import express from 'express'
import bodyParser from 'body-parser'
const app = express();
import {generateKeyPairSync, KeyPairSyncResult, createECDH, createD<PERSON><PERSON><PERSON><PERSON><PERSON>} from "node:crypto"
// import {promisify} from 'node:util';

// 生成后端的 X25519 密钥对
let backendPrivateKey;
let backendPublicKey;


function pemToArrayBuffer(pem) {
    // 去掉 PEM 标签和换行符，只保留 Base64 编码的数据
    const base64 = pem.replace(/-----BEGIN [^-]+-----/, '')
        .replace(/-----END [^-]+-----/, '')
        .replace(/\s+/g, '');

    // 将 Base64 字符串解码为二进制数据
    const binaryString = Buffer.from(base64, 'base64').toString('binary');

    // 创建一个新的 ArrayBuffer，并将二进制字符串中的字符拷贝到其中
    const arrayBuffer = new ArrayBuffer(binaryString.length);
    const uint8Array = new Uint8Array(arrayBuffer);
    for (let i = 0; i < binaryString.length; i++) {
        uint8Array[i] = binaryString.charCodeAt(i);
    }

    return arrayBuffer;
}


(async () => {
    const keyPair = generateKeyPairSync("x25519", {
        publicKeyEncoding: {type: 'spki', format: 'pem'},
        privateKeyEncoding: {type: 'pkcs8', format: 'pem'}
    });
    backendPrivateKey = pemToArrayBuffer(keyPair.privateKey);
    backendPublicKey = pemToArrayBuffer(keyPair.publicKey);
    console.log('keyPair.privateKey', keyPair.privateKey)
    console.log('keyPair.publicKey', keyPair.publicKey)
    // console.log('backendPublicKey', backendPublicKey)

    // const dh = createDiffieHellman(512); // 256 位（32 字节）
    // const publicKey = dh.generateKeys();
    // const privateKey = dh.getPrivateKey();

    // console.log('Public Key:', publicKey.toString('base64'));
    // console.log('Private Key:', privateKey.toString('base64'));
})();

// 中间件
app.use(bodyParser.json());
app.use(express.static('public'));



// 解密接口
app.post('/decrypt', async (req, res) => {
    const {publicKey} = req.body;

    try {
        // 转换 Base64 到 ArrayBuffer
        // const ivBuffer = base64ToArrayBuffer(iv);
        // const ciphertextBuffer = base64ToArrayBuffer(ciphertext);
        const frontendPublicKeyBuffer = hexToUint8Array(publicKey);

        // 创建 ECDH 实例
        const ecdh = createDiffieHellman(1024);;

        ecdh.setPrivateKey(backendPrivateKey);

        const sharedSecret = ecdh.computeSecret(frontendPublicKeyBuffer);

        // 输出共享密钥（通常以十六进制或 Base64 格式）
        console.log('Shared Secret (Hex):', sharedSecret.toString('hex'));
        console.log('Shared Secret (Base64):', sharedSecret.toString('base64'));

    } catch (error) {
        console.error("解密失败:", error);
        res.status(500).json({error: "解密失败"});
    }
});

// 工具函数：Base64 转 ArrayBuffer
function hexToUint8Array(hex) {
    if (typeof hex !== 'string') {
        throw new TypeError('Expected input to be a string');
    }

    if (hex.length % 2 !== 0) {
        throw new RangeError('Expected string to be an even number of characters');
    }

    // Remove any potential leading "0x"
    hex = hex.replace(/^0x/, '');

    const length = hex.length / 2;
    const uint8Array = new Uint8Array(length);

    for (let i = 0; i < hex.length; i += 2) {
        uint8Array[i / 2] = parseInt(hex.substr(i, 2), 16);
    }

    return uint8Array;
}
function base64ToArrayBuffer(base64) {
    const binaryString = Buffer.from(base64, 'base64').toString('binary');
    const bytes = new Uint8Array(binaryString.length);
    for (let i = 0; i < binaryString.length; i++) {
        bytes[i] = binaryString.charCodeAt(i);
    }
    return bytes;
}

app.listen(3000, () => {
    console.log("后端运行在 http://localhost:3000");
});
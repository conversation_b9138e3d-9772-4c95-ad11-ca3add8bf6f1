const crypto = require('node:crypto');

// 1. 密钥交换（X25519）
const alice = crypto.generateKeyPairSync('x25519'); // Alice 生成密钥对
const bob = crypto.generateKeyPairSync('x25519');   // Bob 生成密钥对

// 计算共享密钥
const aliceShared = crypto.diffieHellman({
    privateKey: alice.privateKey,
    publicKey: bob.publicKey,
});


const bobShared = crypto.diffieHellman({
    privateKey: bob.privateKey,
    publicKey: alice.publicKey,
});

// 验证共享密钥是否相同
if (!aliceShared.equals(bobShared)) {
    throw new Error('共享密钥不匹配');
}

// 2. 密钥派生（HKDF）
const hkdf = crypto.createHmac('sha256', 'salt'); // 使用 HMAC-SHA256 派生密钥
hkdf.update(aliceShared);
const derivedKey = hkdf.digest().slice(0, 32); // 派生 32 字节的 AES-256 密钥

// 3. 对称加密（AES-GCM）
const plaintext = 'Hello, this is a secret message!'; // 明文数据
const nonce = crypto.randomBytes(12); // 随机生成 12 字节的 Nonce

// 加密
const cipher = crypto.createCipheriv('aes-256-gcm', derivedKey, nonce);
const encrypted = Buffer.concat([cipher.update(plaintext, 'utf8'), cipher.final()]);
const authTag = cipher.getAuthTag(); // 获取认证标签

// 4. 对称解密
const decipher = crypto.createDecipheriv('aes-256-gcm', derivedKey, nonce);
decipher.setAuthTag(authTag); // 设置认证标签
const decrypted = Buffer.concat([decipher.update(encrypted), decipher.final()]).toString('utf8');

// 输出结果
console.log('明文:', plaintext);
console.log('加密后:', encrypted.toString('hex'));
console.log('解密后:', decrypted);
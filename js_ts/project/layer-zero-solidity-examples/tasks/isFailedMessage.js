module.exports = async function (taskArgs, hre) {
    console.log({ taskArgs })
    const nonBlockingApp = await ethers.getContractAt("NonblockingLzApp", taskArgs.desAddress)

    // concat remote and local address
    let remoteAndLocal = hre.ethers.utils.solidityPack(["address", "address"], [taskArgs.srcAddress, taskArgs.desAddress])

    let bool = await nonBlockingApp.failedMessages(taskArgs.srcChainId, remoteAndLocal, taskArgs.nonce)
    console.log(`failedMessages: ${bool}`)
}

// npx hardhat failedMessage --network fuji --src-chain-id TBD --src-address TBD --des-address TBD --nonce TBD
// npx hardhat failedMessage --network fuji --src-chain-id 101 --src-address ****************************************** --des-address ****************************************** --nonce 10

# Design Document

## Overview

This design enhances the existing email parser to support stream-based processing while maintaining backward compatibility with the current string-based API. The solution addresses memory efficiency issues when processing large mbox files by implementing Node.js Transform streams that can handle email parsing incrementally.

## Architecture

### Current Architecture Issues
- Loads entire files into memory as strings
- Uses synchronous regex operations on large strings
- No backpressure handling for large files
- Memory usage scales linearly with file size

### New Stream-Based Architecture
The enhanced architecture introduces:
- **EmailParserStream**: A Transform stream for processing email data incrementally
- **MboxSplitterStream**: A specialized stream for splitting mbox format into individual emails
- **EmlParserStream**: A stream for processing single EML files
- **Hybrid API**: Maintains existing string-based functions while adding stream alternatives

## Components and Interfaces

### Core Stream Classes

#### EmailParserStream
```typescript
class EmailParserStream extends Transform {
  constructor(options?: EmailParserOptions)
  _transform(chunk: Buffer, encoding: string, callback: TransformCallback): void
  _flush(callback: TransformCallback): void
}

interface EmailParserOptions {
  format?: 'auto' | 'mbox' | 'eml';
  highWaterMark?: number;
  objectMode?: boolean;
}
```

#### MboxSplitterStream
```typescript
class MboxSplitterStream extends Transform {
  private buffer: string = '';
  private emailCount: number = 0;
  
  constructor(options?: StreamOptions)
  _transform(chunk: Buffer, encoding: string, callback: TransformCallback): void
  _flush(callback: TransformCallback): void
  private processBuffer(): void
  private findEmailBoundaries(data: string): number[]
}
```

#### EmlParserStream
```typescript
class EmlParserStream extends Transform {
  private buffer: string = '';
  
  constructor(options?: StreamOptions)
  _transform(chunk: Buffer, encoding: string, callback: TransformCallback): void
  _flush(callback: TransformCallback): void
}
```

### Enhanced API Functions

#### Stream-Based Functions
```typescript
// Stream-based parsing functions
function parseEmailFileStream(filePath: string, options?: EmailParserOptions): EmailParserStream
function parseEmailStream(inputStream: Readable, options?: EmailParserOptions): EmailParserStream
function parseMboxStream(inputStream: Readable): MboxSplitterStream
function parseEmlStream(inputStream: Readable): EmlParserStream

// Utility functions
function createEmailParser(format: 'mbox' | 'eml' | 'auto'): EmailParserStream
function detectFormatFromStream(stream: Readable): Promise<'mbox' | 'eml'>
```

#### Backward Compatibility
```typescript
// Existing functions remain unchanged
function parseEmailFile(content: string, filename?: string): ParsedEmail[]
function parseEml(emlString: string): ParsedEmail
function parseMbox(mboxString: string): ParsedEmail[]
function parseEmail(emailString: string): ParsedEmail
```

## Data Models

### Stream Processing State
```typescript
interface StreamParserState {
  buffer: string;
  currentEmail: string;
  emailBoundaryPattern: RegExp;
  isProcessingEmail: boolean;
  bytesProcessed: number;
  emailsEmitted: number;
}

interface EmailBoundary {
  start: number;
  end: number;
  type: 'mbox-from' | 'email-end';
}
```

### Enhanced ParsedEmail Interface
The existing `ParsedEmail` interface remains unchanged to maintain compatibility:
```typescript
interface ParsedEmail {
  subject: string | null;
  html: string | null;
  htmlHeaders: Record<string, string> | null;
  attachment: any[];
  from: string | null;
  to: string | null;
  datetime: Date | null;
}
```

## Error Handling

### Stream Error Management
- **Parsing Errors**: Individual email parsing failures don't stop the stream
- **Memory Errors**: Implement buffer size limits and backpressure handling
- **Format Errors**: Graceful degradation when format detection fails
- **IO Errors**: Proper error propagation from file streams

### Error Recovery Strategies
```typescript
interface ErrorHandlingOptions {
  continueOnError: boolean;
  maxBufferSize: number;
  errorCallback?: (error: Error, context: string) => void;
}
```

## Testing Strategy

### Unit Tests
- **Buffer Management**: Test chunk boundary handling
- **Email Boundary Detection**: Test mbox "From " line detection across chunks
- **Format Detection**: Test auto-detection with partial data
- **Error Scenarios**: Test malformed emails and recovery

### Integration Tests
- **Large File Processing**: Test with multi-GB mbox files
- **Memory Usage**: Monitor memory consumption during processing
- **Performance Comparison**: Benchmark against string-based implementation
- **Backpressure**: Test with slow downstream consumers

### Test Data Sets
- Small mbox files (< 1MB)
- Medium mbox files (10-100MB)
- Large mbox files (> 1GB)
- Mixed format files
- Malformed email files

## Implementation Details

### Buffer Management Strategy
1. **Chunk Processing**: Process data in configurable chunks (default 64KB)
2. **Boundary Detection**: Maintain overlap buffer to handle split boundaries
3. **Memory Limits**: Implement maximum buffer size with overflow handling
4. **Backpressure**: Respect downstream processing speed

### Mbox Boundary Detection Algorithm
```typescript
private findMboxBoundaries(data: string): EmailBoundary[] {
  const boundaries: EmailBoundary[] = [];
  const fromLineRegex = /^From .+$/gm;
  let match: RegExpExecArray | null;
  
  while ((match = fromLineRegex.exec(data)) !== null) {
    boundaries.push({
      start: match.index,
      end: match.index + match[0].length,
      type: 'mbox-from'
    });
  }
  
  return boundaries;
}
```

### Stream Pipeline Architecture
```
File/Input Stream → EmailParserStream → ParsedEmail Objects
                      ↓
                 MboxSplitterStream (if mbox)
                      ↓
                 Individual Email Strings
                      ↓
                 parseEmail() function
                      ↓
                 ParsedEmail Objects
```

## Performance Considerations

### Memory Optimization
- **Constant Memory Usage**: O(1) memory usage regardless of file size
- **Buffer Reuse**: Reuse buffers to minimize garbage collection
- **Lazy Processing**: Process emails only when downstream is ready

### Processing Efficiency
- **Incremental Parsing**: Parse emails as soon as complete data is available
- **Parallel Processing**: Support multiple concurrent streams
- **Caching**: Cache compiled regex patterns and parsing utilities

### Benchmarking Targets
- **Memory Usage**: < 100MB for any file size
- **Processing Speed**: > 1000 emails/second for typical email sizes
- **Startup Time**: < 100ms for stream initialization
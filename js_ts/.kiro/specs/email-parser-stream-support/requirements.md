# Requirements Document

## Introduction

The current email parser implementation loads entire email files into memory as strings, which creates performance bottlenecks and memory issues when processing large mbox files (which can be several gigabytes). This feature will enhance the email parser to support stream-based processing, enabling efficient handling of large email files with minimal memory footprint.

## Requirements

### Requirement 1

**User Story:** As a developer processing large mbox files, I want the email parser to use streams instead of loading entire files into memory, so that I can handle gigabyte-sized email archives without running out of memory.

#### Acceptance Criteria

1. WHEN processing an mbox file THEN the parser SHALL use Node.js streams to read the file incrementally
2. WHEN parsing large files THEN memory usage SHALL remain constant regardless of file size
3. WHEN using stream-based parsing THEN the parser SHALL maintain the same output format as the current string-based implementation

### Requirement 2

**User Story:** As a developer, I want both stream and string-based parsing options available, so that I can choose the appropriate method based on my use case and file size.

#### Acceptance Criteria

1. WH<PERSON> using the parser THEN both stream-based and string-based APIs SHALL be available
2. WHEN processing small files THEN developers SHALL be able to use the existing string-based API for simplicity
3. WHEN processing large files THEN developers SHALL be able to use the new stream-based API for efficiency
4. WHEN switching between APIs THEN the output format SHALL remain consistent

### Requirement 3

**User Story:** As a developer, I want the stream-based parser to handle mbox format correctly, so that individual emails are properly separated and parsed even when split across stream chunks.

#### Acceptance Criteria

1. WHEN an mbox "From " delimiter spans across stream chunks THEN the parser SHALL correctly identify email boundaries
2. WHEN processing streaming data THEN each complete email SHALL be emitted as soon as it's fully read
3. WHEN encountering malformed email boundaries THEN the parser SHALL handle errors gracefully without crashing

### Requirement 4

**User Story:** As a developer, I want the stream-based parser to support both mbox and eml formats, so that I can process any email file type efficiently.

#### Acceptance Criteria

1. WHEN the input is an eml file stream THEN the parser SHALL process it as a single email
2. WHEN the input is an mbox file stream THEN the parser SHALL split it into individual emails
3. WHEN format detection is needed THEN the parser SHALL determine format from initial stream data or filename

### Requirement 5

**User Story:** As a developer, I want error handling and backpressure support in the stream implementation, so that the parser remains stable under various conditions.

#### Acceptance Criteria

1. WHEN downstream processing is slow THEN the parser SHALL respect backpressure and pause reading
2. WHEN stream errors occur THEN the parser SHALL emit appropriate error events
3. WHEN parsing fails for individual emails THEN the parser SHALL continue processing remaining emails
4. WHEN memory limits are approached THEN the parser SHALL implement appropriate flow control